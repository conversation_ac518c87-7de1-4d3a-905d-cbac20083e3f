// ===== SEAT MAP COMPONENT =====

class SeatMap {
    constructor(container, showtimeId, options = {}) {
        this.container = typeof container === 'string' ? utils.$(container) : container;
        this.showtimeId = showtimeId;
        this.options = {
            maxSeats: 8,
            seatPrice: 100000,
            vipPrice: 150000,
            couplePrice: 200000,
            onSeatSelect: null,
            onSeatDeselect: null,
            onSelectionChange: null,
            ...options
        };
        
        this.seatData = null;
        this.selectedSeats = [];
        this.reservedSeats = [];
        this.seatElements = new Map();
        this.isLoading = false;
        
        this.init();
    }

    // Initialize seat map
    async init() {
        if (!this.container) {
            console.error('SeatMap container not found');
            return;
        }

        try {
            this.showLoading();
            await this.loadSeatData();
            this.render();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize seat map:', error);
            this.showError('<PERSON><PERSON><PERSON><PERSON> thể tải sơ đồ ghế. Vui lòng thử lại.');
        }
    }

    // Load seat data from API
    async loadSeatData() {
        try {
            this.seatData = await showtimeAPI.getSeatMap(this.showtimeId);
            this.reservedSeats = this.seatData.reservedSeats || [];
        } catch (error) {
            console.error('Failed to load seat data:', error);
            throw error;
        }
    }

    // Render seat map
    render() {
        if (!this.seatData) return;

        const { rows, seatsPerRow, seatTypes } = this.seatData;
        
        this.container.innerHTML = `
            <div class="seatmap-container">
                <div class="screen-container">
                    <div class="screen">
                        <span>MÀN HÌNH</span>
                    </div>
                </div>
                
                <div class="seatmap-grid" id="seatmap-grid">
                    ${this.renderSeatGrid(rows, seatsPerRow, seatTypes)}
                </div>
                
                <div class="seatmap-legend">
                    <div class="legend-item">
                        <div class="seat-demo seat-available"></div>
                        <span>Trống</span>
                    </div>
                    <div class="legend-item">
                        <div class="seat-demo seat-selected"></div>
                        <span>Đang chọn</span>
                    </div>
                    <div class="legend-item">
                        <div class="seat-demo seat-reserved"></div>
                        <span>Đã đặt</span>
                    </div>
                    <div class="legend-item">
                        <div class="seat-demo seat-vip"></div>
                        <span>VIP</span>
                    </div>
                    <div class="legend-item">
                        <div class="seat-demo seat-couple"></div>
                        <span>Couple</span>
                    </div>
                </div>
                
                <div class="selection-summary" id="selection-summary">
                    <div class="selected-seats-info">
                        <span class="selected-count">0</span> ghế đã chọn
                    </div>
                    <div class="total-price">
                        Tổng: <span class="price-amount">0đ</span>
                    </div>
                </div>
            </div>
        `;

        this.cacheElements();
    }

    // Render seat grid
    renderSeatGrid(rows, seatsPerRow, seatTypes) {
        let gridHTML = '';
        
        for (let row = 0; row < rows.length; row++) {
            const rowData = rows[row];
            gridHTML += `<div class="seat-row" data-row="${row}">`;
            gridHTML += `<div class="row-label">${rowData.name}</div>`;
            
            for (let seat = 0; seat < seatsPerRow; seat++) {
                const seatId = `${rowData.name}${seat + 1}`;
                const seatType = this.getSeatType(row, seat, seatTypes);
                const isReserved = this.reservedSeats.includes(seatId);
                const isAvailable = !isReserved && seatType !== 'disabled';
                
                gridHTML += `
                    <div class="seat ${this.getSeatClasses(seatType, isReserved, isAvailable)}" 
                         data-seat-id="${seatId}"
                         data-row="${row}"
                         data-seat="${seat}"
                         data-type="${seatType}"
                         ${isAvailable ? 'tabindex="0"' : ''}>
                        <span class="seat-number">${seat + 1}</span>
                    </div>
                `;
            }
            
            gridHTML += '</div>';
        }
        
        return gridHTML;
    }

    // Get seat type based on position and configuration
    getSeatType(row, seat, seatTypes) {
        // Check if seat is disabled (aisle, etc.)
        if (seatTypes.disabled && seatTypes.disabled.some(pos => pos.row === row && pos.seat === seat)) {
            return 'disabled';
        }
        
        // Check VIP seats
        if (seatTypes.vip && seatTypes.vip.some(pos => pos.row === row && pos.seat === seat)) {
            return 'vip';
        }
        
        // Check couple seats
        if (seatTypes.couple && seatTypes.couple.some(pos => pos.row === row && pos.seat === seat)) {
            return 'couple';
        }
        
        return 'standard';
    }

    // Get CSS classes for seat
    getSeatClasses(seatType, isReserved, isAvailable) {
        let classes = [];
        
        if (!isAvailable || seatType === 'disabled') {
            classes.push('seat-disabled');
        } else if (isReserved) {
            classes.push('seat-reserved');
        } else {
            classes.push('seat-available');
            
            switch (seatType) {
                case 'vip':
                    classes.push('seat-vip');
                    break;
                case 'couple':
                    classes.push('seat-couple');
                    break;
                default:
                    classes.push('seat-standard');
            }
        }
        
        return classes.join(' ');
    }

    // Cache DOM elements
    cacheElements() {
        this.seatGrid = utils.$('#seatmap-grid');
        this.selectionSummary = utils.$('#selection-summary');
        this.selectedCountEl = this.container.querySelector('.selected-count');
        this.priceAmountEl = this.container.querySelector('.price-amount');
        
        // Cache all seat elements
        const seatElements = this.container.querySelectorAll('.seat[data-seat-id]');
        seatElements.forEach(seat => {
            const seatId = seat.dataset.seatId;
            this.seatElements.set(seatId, seat);
        });
    }

    // Setup event listeners
    setupEventListeners() {
        // Seat click handlers
        this.seatGrid.addEventListener('click', (e) => {
            const seat = e.target.closest('.seat');
            if (seat && seat.dataset.seatId) {
                this.handleSeatClick(seat);
            }
        });

        // Keyboard navigation
        this.seatGrid.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                const seat = e.target.closest('.seat');
                if (seat && seat.dataset.seatId) {
                    e.preventDefault();
                    this.handleSeatClick(seat);
                }
            }
        });
    }

    // Handle seat click
    handleSeatClick(seatElement) {
        const seatId = seatElement.dataset.seatId;
        const seatType = seatElement.dataset.type;
        
        // Check if seat is available
        if (!seatElement.classList.contains('seat-available')) {
            return;
        }

        // Check if seat is already selected
        if (this.selectedSeats.includes(seatId)) {
            this.deselectSeat(seatId);
        } else {
            this.selectSeat(seatId, seatType);
        }
    }

    // Select a seat
    selectSeat(seatId, seatType) {
        // Check max seats limit
        if (this.selectedSeats.length >= this.options.maxSeats) {
            Toast.warning(`Bạn chỉ có thể chọn tối đa ${this.options.maxSeats} ghế`);
            return;
        }

        // Add to selected seats
        this.selectedSeats.push(seatId);
        
        // Update seat element
        const seatElement = this.seatElements.get(seatId);
        if (seatElement) {
            seatElement.classList.remove('seat-available');
            seatElement.classList.add('seat-selected');
        }

        // Update summary
        this.updateSelectionSummary();

        // Call callbacks
        if (this.options.onSeatSelect) {
            this.options.onSeatSelect(seatId, seatType);
        }
        
        if (this.options.onSelectionChange) {
            this.options.onSelectionChange(this.selectedSeats);
        }
    }

    // Deselect a seat
    deselectSeat(seatId) {
        // Remove from selected seats
        this.selectedSeats = this.selectedSeats.filter(id => id !== seatId);
        
        // Update seat element
        const seatElement = this.seatElements.get(seatId);
        if (seatElement) {
            seatElement.classList.remove('seat-selected');
            seatElement.classList.add('seat-available');
        }

        // Update summary
        this.updateSelectionSummary();

        // Call callbacks
        if (this.options.onSeatDeselect) {
            this.options.onSeatDeselect(seatId);
        }
        
        if (this.options.onSelectionChange) {
            this.options.onSelectionChange(this.selectedSeats);
        }
    }

    // Update selection summary
    updateSelectionSummary() {
        const selectedCount = this.selectedSeats.length;
        const totalPrice = this.calculateTotalPrice();

        if (this.selectedCountEl) {
            this.selectedCountEl.textContent = selectedCount;
        }

        if (this.priceAmountEl) {
            this.priceAmountEl.textContent = utils.formatPrice(totalPrice);
        }
    }

    // Calculate total price
    calculateTotalPrice() {
        let total = 0;
        
        this.selectedSeats.forEach(seatId => {
            const seatElement = this.seatElements.get(seatId);
            if (seatElement) {
                const seatType = seatElement.dataset.type;
                
                switch (seatType) {
                    case 'vip':
                        total += this.options.vipPrice;
                        break;
                    case 'couple':
                        total += this.options.couplePrice;
                        break;
                    default:
                        total += this.options.seatPrice;
                }
            }
        });
        
        return total;
    }

    // Get selected seats
    getSelectedSeats() {
        return [...this.selectedSeats];
    }

    // Clear selection
    clearSelection() {
        this.selectedSeats.forEach(seatId => {
            const seatElement = this.seatElements.get(seatId);
            if (seatElement) {
                seatElement.classList.remove('seat-selected');
                seatElement.classList.add('seat-available');
            }
        });
        
        this.selectedSeats = [];
        this.updateSelectionSummary();
    }

    // Show loading
    showLoading() {
        this.isLoading = true;
        this.container.innerHTML = `
            <div class="seatmap-loading">
                <div class="spinner"></div>
                <p>Đang tải sơ đồ ghế...</p>
            </div>
        `;
    }

    // Hide loading
    hideLoading() {
        this.isLoading = false;
    }

    // Show error
    showError(message) {
        this.container.innerHTML = `
            <div class="seatmap-error">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="this.init()">
                    Thử lại
                </button>
            </div>
        `;
    }

    // Destroy seat map
    destroy() {
        this.selectedSeats = [];
        this.seatElements.clear();
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Export to global scope
window.SeatMap = SeatMap;
