// ===== COMING SOON PAGE =====

window.pages.comingSoon = {
    movies: [],
    filteredMovies: [],
    currentGenre: 'all',

    async render() {
        const mainContent = utils.$('#main-content');
        
        try {
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="page-loading">
                        <div class="spinner"></div>
                        <p><PERSON>ang tải danh sách phim sắp chiếu...</p>
                    </div>
                </div>
            `;

            // Load coming soon movies
            await this.loadComingSoonMovies();
            
            // Render page
            this.renderComingSoonPage();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to render coming soon page:', error);
            this.renderError('Có lỗi xảy ra khi tải danh sách phim sắp chiếu.');
        }
    },

    async loadComingSoonMovies() {
        try {
            // Load movies with status 'coming-soon'
            const allMovies = await movieAPI.getMovies();
            this.movies = allMovies.filter(movie => movie.status === 'coming-soon');
            this.filteredMovies = [...this.movies];
            
        } catch (error) {
            console.error('Failed to load coming soon movies:', error);
            this.movies = [];
            this.filteredMovies = [];
        }
    },

    renderComingSoonPage() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="coming-soon-page">
                <!-- Hero Section -->
                <div class="hero-section">
                    <div class="hero-background">
                        <div class="hero-overlay"></div>
                    </div>
                    <div class="container">
                        <div class="hero-content">
                            <h1 class="hero-title">
                                <i class="fas fa-calendar-alt"></i>
                                Phim sắp chiếu
                            </h1>
                            <p class="hero-subtitle">
                                Khám phá những bộ phim blockbuster sắp ra mắt tại rạp chiếu phim
                            </p>
                            <div class="hero-stats">
                                <div class="stat-item">
                                    <span class="stat-number">${this.movies.length}</span>
                                    <span class="stat-label">Phim sắp chiếu</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">${this.getUniqueGenres().length}</span>
                                    <span class="stat-label">Thể loại</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="filters-section">
                    <div class="container">
                        <div class="filters-header">
                            <h2>Lọc theo thể loại</h2>
                            <div class="results-count">
                                Hiển thị ${this.filteredMovies.length} phim
                            </div>
                        </div>
                        
                        <div class="genre-filters">
                            <button class="filter-btn ${this.currentGenre === 'all' ? 'active' : ''}" 
                                    data-genre="all">
                                Tất cả
                            </button>
                            ${this.getUniqueGenres().map(genre => `
                                <button class="filter-btn ${this.currentGenre === genre ? 'active' : ''}" 
                                        data-genre="${genre}">
                                    ${genre}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <!-- Movies Grid -->
                <div class="movies-section">
                    <div class="container">
                        ${this.filteredMovies.length > 0 ? `
                            <div class="movies-grid">
                                ${this.filteredMovies.map(movie => this.renderMovieCard(movie)).join('')}
                            </div>
                        ` : `
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-film"></i>
                                </div>
                                <h3>Không có phim nào</h3>
                                <p>Hiện tại không có phim sắp chiếu trong thể loại này.</p>
                                <button class="btn btn-primary" onclick="window.pages.comingSoon.filterByGenre('all')">
                                    Xem tất cả phim
                                </button>
                            </div>
                        `}
                    </div>
                </div>

                <!-- Newsletter Section -->
                <div class="newsletter-section">
                    <div class="container">
                        <div class="newsletter-content">
                            <div class="newsletter-text">
                                <h3>Đăng ký nhận thông báo</h3>
                                <p>Nhận thông tin về phim mới và ưu đãi đặc biệt</p>
                            </div>
                            <form class="newsletter-form" id="newsletter-form">
                                <div class="newsletter-input-group">
                                    <input type="email" class="form-input" placeholder="Nhập email của bạn" required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-bell"></i>
                                        Đăng ký
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderMovieCard(movie) {
        const releaseDate = new Date(movie.releaseDate);
        const now = new Date();
        const daysUntilRelease = Math.ceil((releaseDate - now) / (1000 * 60 * 60 * 24));
        
        return `
            <div class="movie-card coming-soon-card" data-movie-id="${movie.id}">
                <div class="movie-poster-container">
                    <img src="${movie.poster}" alt="${movie.title}" class="movie-poster">
                    <div class="movie-overlay">
                        <div class="movie-actions">
                            <button class="btn btn-primary btn-sm" onclick="window.pages.comingSoon.viewMovieDetail(${movie.id})">
                                <i class="fas fa-eye"></i>
                                Xem chi tiết
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="window.pages.comingSoon.setReminder(${movie.id})">
                                <i class="fas fa-bell"></i>
                                Nhắc nhở
                            </button>
                        </div>
                    </div>
                    <div class="release-countdown">
                        ${daysUntilRelease > 0 ? `
                            <span class="countdown-badge">
                                <i class="fas fa-calendar"></i>
                                Còn ${daysUntilRelease} ngày
                            </span>
                        ` : `
                            <span class="countdown-badge available">
                                <i class="fas fa-play"></i>
                                Đã ra mắt
                            </span>
                        `}
                    </div>
                </div>
                
                <div class="movie-info">
                    <h3 class="movie-title">${movie.title}</h3>
                    <div class="movie-meta">
                        <span class="movie-genre">
                            <i class="fas fa-tag"></i>
                            ${movie.genre}
                        </span>
                        <span class="movie-duration">
                            <i class="fas fa-clock"></i>
                            ${movie.duration} phút
                        </span>
                    </div>
                    <div class="movie-release">
                        <i class="fas fa-calendar-alt"></i>
                        Khởi chiếu: ${utils.formatDate(movie.releaseDate)}
                    </div>
                    ${movie.rating ? `
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>${movie.rating}/10</span>
                        </div>
                    ` : ''}
                    <p class="movie-description">
                        ${movie.description ? utils.truncateText(movie.description, 100) : 'Thông tin phim sẽ được cập nhật sớm...'}
                    </p>
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Genre filter buttons
        const filterBtns = utils.$$('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const genre = e.target.dataset.genre;
                this.filterByGenre(genre);
            });
        });

        // Newsletter form
        const newsletterForm = utils.$('#newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.subscribeNewsletter();
            });
        }

        // Movie card hover effects
        const movieCards = utils.$$('.movie-card');
        movieCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('hovered');
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('hovered');
            });
        });
    },

    filterByGenre(genre) {
        this.currentGenre = genre;
        
        if (genre === 'all') {
            this.filteredMovies = [...this.movies];
        } else {
            this.filteredMovies = this.movies.filter(movie => movie.genre === genre);
        }
        
        // Re-render the page
        this.renderComingSoonPage();
        this.setupEventHandlers();
    },

    getUniqueGenres() {
        const genres = [...new Set(this.movies.map(movie => movie.genre))];
        return genres.sort();
    },

    viewMovieDetail(movieId) {
        window.router.navigate(`/movies/${movieId}`);
    },

    async setReminder(movieId) {
        try {
            const movie = this.movies.find(m => m.id === movieId);
            if (!movie) return;

            if (!auth.isAuthenticated()) {
                const shouldLogin = await Modal.confirm(
                    'Bạn cần đăng nhập để đặt nhắc nhở. Đăng nhập ngay?',
                    { title: 'Yêu cầu đăng nhập' }
                );
                
                if (shouldLogin) {
                    window.router.navigate('/login');
                }
                return;
            }

            // Set reminder
            await api.post('/api/reminders', {
                movieId: movieId,
                type: 'release_date'
            });

            Toast.success(`Đã đặt nhắc nhở cho phim "${movie.title}"`);
            
        } catch (error) {
            Toast.error('Không thể đặt nhắc nhở. Vui lòng thử lại.');
        }
    },

    async subscribeNewsletter() {
        const form = utils.$('#newsletter-form');
        const emailInput = form.querySelector('input[type="email"]');
        const email = emailInput.value.trim();

        if (!email || !utils.validators.email(email)) {
            Toast.error('Vui lòng nhập email hợp lệ');
            return;
        }

        try {
            await api.post('/api/newsletter/subscribe', { email });
            Toast.success('Đăng ký nhận thông báo thành công!');
            emailInput.value = '';
            
        } catch (error) {
            if (error.message.includes('already subscribed')) {
                Toast.warning('Email này đã được đăng ký trước đó');
            } else {
                Toast.error('Không thể đăng ký. Vui lòng thử lại.');
            }
        }
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải trang</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.pages.comingSoon.render()">
                                Thử lại
                            </button>
                            <a href="#" class="btn btn-secondary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
