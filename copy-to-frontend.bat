@echo off
echo Copying vanilla-version files to frontend...

:: Create directories
mkdir frontend\css 2>nul
mkdir frontend\js 2>nul
mkdir frontend\js\components 2>nul
mkdir frontend\js\pages 2>nul
mkdir frontend\assets 2>nul

:: Copy CSS files
copy "vanilla-version\css\main.css" "frontend\css\" >nul
copy "vanilla-version\css\components.css" "frontend\css\" >nul
copy "vanilla-version\css\responsive.css" "frontend\css\" >nul

:: Copy JS core files
copy "vanilla-version\js\app.js" "frontend\js\" >nul
copy "vanilla-version\js\api.js" "frontend\js\" >nul
copy "vanilla-version\js\auth.js" "frontend\js\" >nul
copy "vanilla-version\js\router.js" "frontend\js\" >nul
copy "vanilla-version\js\utils.js" "frontend\js\" >nul

:: Copy J<PERSON> components
copy "vanilla-version\js\components\*.js" "frontend\js\components\" >nul

:: Copy JS pages
copy "vanilla-version\js\pages\*.js" "frontend\js\pages\" >nul

:: Copy other files
copy "vanilla-version\test-features.html" "frontend\" >nul
copy "vanilla-version\final-test.html" "frontend\" >nul

echo ✓ Files copied successfully!
echo.
echo Next steps:
echo 1. cd frontend
echo 2. npm install
echo 3. npm run dev
echo.
pause
