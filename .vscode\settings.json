{"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.tsx": "${capture}.js"}}