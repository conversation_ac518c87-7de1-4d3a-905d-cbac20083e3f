// ===== BOOKING FORM COMPONENT =====

class BookingForm {
    constructor(options = {}) {
        this.options = {
            showtimeId: null,
            selectedSeats: [],
            totalPrice: 0,
            onSuccess: () => {},
            onBack: () => {},
            ...options
        };
        
        this.formData = {
            customerName: '',
            customerPhone: '',
            customerEmail: '',
            paymentMethod: 'cash',
            promoCode: ''
        };
        
        this.promoDiscount = 0;
        this.appliedPromo = '';
        this.isSubmitting = false;
    }

    render() {
        return `
            <div class="booking-form-container">
                <div class="booking-form-header">
                    <h3>
                        <i class="fas fa-user"></i>
                        Thông tin đặt vé
                    </h3>
                    <p>Vui lòng điền đầy đủ thông tin để hoàn tất đặt vé</p>
                </div>

                <form id="booking-form" class="booking-form">
                    ${this.renderCustomerInfo()}
                    ${this.renderPaymentMethod()}
                    ${this.renderPromoCode()}
                    ${this.renderOrderSummary()}
                    ${this.renderFormActions()}
                </form>
            </div>
        `;
    }

    renderCustomerInfo() {
        return `
            <div class="form-section">
                <h4 class="form-section-title">
                    <i class="fas fa-user"></i>
                    Thông tin khách hàng
                </h4>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="customer-name">
                            Họ và tên *
                        </label>
                        <input 
                            type="text" 
                            id="customer-name" 
                            name="customerName"
                            class="form-input" 
                            placeholder="Nhập họ và tên"
                            value="${this.formData.customerName}"
                            required>
                        <div class="form-error" id="customer-name-error"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="customer-phone">
                            Số điện thoại *
                        </label>
                        <input 
                            type="tel" 
                            id="customer-phone" 
                            name="customerPhone"
                            class="form-input" 
                            placeholder="Nhập số điện thoại"
                            value="${this.formData.customerPhone}"
                            pattern="[0-9]{10}"
                            required>
                        <div class="form-error" id="customer-phone-error"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="customer-email">
                        Email *
                    </label>
                    <input 
                        type="email" 
                        id="customer-email" 
                        name="customerEmail"
                        class="form-input" 
                        placeholder="Nhập địa chỉ email"
                        value="${this.formData.customerEmail}"
                        required>
                    <div class="form-error" id="customer-email-error"></div>
                </div>
            </div>
        `;
    }

    renderPaymentMethod() {
        const paymentMethods = [
            { value: 'cash', label: 'Tiền mặt', icon: 'fas fa-money-bill-wave', description: 'Thanh toán tại quầy' },
            { value: 'card', label: 'Thẻ tín dụng', icon: 'fas fa-credit-card', description: 'Visa, Mastercard' },
            { value: 'momo', label: 'MoMo', icon: 'fas fa-mobile-alt', description: 'Ví điện tử MoMo' },
            { value: 'banking', label: 'Chuyển khoản', icon: 'fas fa-university', description: 'Internet Banking' }
        ];

        return `
            <div class="form-section">
                <h4 class="form-section-title">
                    <i class="fas fa-credit-card"></i>
                    Phương thức thanh toán
                </h4>
                
                <div class="payment-methods">
                    ${paymentMethods.map(method => `
                        <label class="payment-method ${this.formData.paymentMethod === method.value ? 'selected' : ''}">
                            <input 
                                type="radio" 
                                name="paymentMethod" 
                                value="${method.value}"
                                ${this.formData.paymentMethod === method.value ? 'checked' : ''}>
                            <div class="payment-method-content">
                                <div class="payment-method-icon">
                                    <i class="${method.icon}"></i>
                                </div>
                                <div class="payment-method-info">
                                    <div class="payment-method-label">${method.label}</div>
                                    <div class="payment-method-description">${method.description}</div>
                                </div>
                            </div>
                        </label>
                    `).join('')}
                </div>
            </div>
        `;
    }

    renderPromoCode() {
        return `
            <div class="form-section">
                <h4 class="form-section-title">
                    <i class="fas fa-tag"></i>
                    Mã khuyến mãi
                </h4>
                
                <div class="promo-code-section">
                    <div class="promo-input-group">
                        <input 
                            type="text" 
                            id="promo-code" 
                            name="promoCode"
                            class="form-input" 
                            placeholder="Nhập mã khuyến mãi"
                            value="${this.formData.promoCode}">
                        <button 
                            type="button" 
                            class="btn btn-secondary" 
                            id="apply-promo-btn"
                            ${this.isSubmitting ? 'disabled' : ''}>
                            Áp dụng
                        </button>
                    </div>
                    
                    ${this.appliedPromo ? `
                        <div class="applied-promo">
                            <div class="promo-success">
                                <i class="fas fa-check-circle"></i>
                                <span>Đã áp dụng mã "${this.appliedPromo}"</span>
                                <button type="button" class="remove-promo-btn" id="remove-promo-btn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="promo-discount">
                                Giảm: ${utils.formatPrice(this.promoDiscount)}
                            </div>
                        </div>
                    ` : ''}
                    
                    <div class="form-error" id="promo-code-error"></div>
                </div>
            </div>
        `;
    }

    renderOrderSummary() {
        const { selectedSeats, totalPrice } = this.options;
        const finalPrice = totalPrice - this.promoDiscount;

        return `
            <div class="form-section">
                <h4 class="form-section-title">
                    <i class="fas fa-receipt"></i>
                    Tóm tắt đơn hàng
                </h4>
                
                <div class="order-summary">
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="fas fa-chair"></i>
                            Ghế đã chọn:
                        </span>
                        <span class="summary-value">${selectedSeats.join(', ')}</span>
                    </div>
                    
                    <div class="summary-item">
                        <span class="summary-label">Số lượng vé:</span>
                        <span class="summary-value">${selectedSeats.length} vé</span>
                    </div>
                    
                    <div class="summary-item">
                        <span class="summary-label">Tổng tiền vé:</span>
                        <span class="summary-value">${utils.formatPrice(totalPrice)}</span>
                    </div>
                    
                    ${this.promoDiscount > 0 ? `
                        <div class="summary-item discount">
                            <span class="summary-label">Giảm giá:</span>
                            <span class="summary-value">-${utils.formatPrice(this.promoDiscount)}</span>
                        </div>
                    ` : ''}
                    
                    <div class="summary-divider"></div>
                    
                    <div class="summary-item total">
                        <span class="summary-label">Tổng thanh toán:</span>
                        <span class="summary-value">${utils.formatPrice(finalPrice)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    renderFormActions() {
        return `
            <div class="form-actions">
                <button 
                    type="button" 
                    class="btn btn-secondary" 
                    id="back-btn"
                    ${this.isSubmitting ? 'disabled' : ''}>
                    <i class="fas fa-arrow-left"></i>
                    Quay lại
                </button>
                
                <button 
                    type="submit" 
                    class="btn btn-primary" 
                    id="submit-btn"
                    ${this.isSubmitting ? 'disabled' : ''}>
                    ${this.isSubmitting ? `
                        <i class="fas fa-spinner fa-spin"></i>
                        Đang xử lý...
                    ` : `
                        <i class="fas fa-check"></i>
                        Xác nhận đặt vé
                    `}
                </button>
            </div>
        `;
    }

    setupEventHandlers() {
        const form = utils.$('#booking-form');
        if (!form) return;

        // Form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // Input validation
        const inputs = form.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });

        // Payment method selection
        const paymentInputs = form.querySelectorAll('input[name="paymentMethod"]');
        paymentInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.formData.paymentMethod = input.value;
                this.updatePaymentMethodUI();
            });
        });

        // Promo code
        const applyPromoBtn = utils.$('#apply-promo-btn');
        if (applyPromoBtn) {
            applyPromoBtn.addEventListener('click', () => this.applyPromoCode());
        }

        const removePromoBtn = utils.$('#remove-promo-btn');
        if (removePromoBtn) {
            removePromoBtn.addEventListener('click', () => this.removePromoCode());
        }

        // Back button
        const backBtn = utils.$('#back-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => this.options.onBack());
        }
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        switch (fieldName) {
            case 'customerName':
                if (value.length < 2) {
                    isValid = false;
                    errorMessage = 'Tên phải có ít nhất 2 ký tự';
                }
                break;
            
            case 'customerPhone':
                if (!/^[0-9]{10}$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Số điện thoại phải có 10 chữ số';
                }
                break;
            
            case 'customerEmail':
                if (!utils.validators.email(value)) {
                    isValid = false;
                    errorMessage = 'Email không hợp lệ';
                }
                break;
        }

        this.showFieldError(field, isValid ? '' : errorMessage);
        return isValid;
    }

    showFieldError(field, message) {
        const errorElement = utils.$(`#${field.id}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = message ? 'block' : 'none';
        }
        
        field.classList.toggle('error', !!message);
    }

    clearFieldError(field) {
        this.showFieldError(field, '');
    }

    updatePaymentMethodUI() {
        const paymentMethods = utils.$$('.payment-method');
        paymentMethods.forEach(method => {
            const input = method.querySelector('input[type="radio"]');
            method.classList.toggle('selected', input.checked);
        });
    }

    async applyPromoCode() {
        const promoInput = utils.$('#promo-code');
        const promoCode = promoInput.value.trim();
        
        if (!promoCode) {
            Toast.warning('Vui lòng nhập mã khuyến mãi');
            return;
        }

        try {
            const response = await api.post('/api/promo/validate', {
                code: promoCode,
                totalAmount: this.options.totalPrice
            });

            this.promoDiscount = response.discount;
            this.appliedPromo = promoCode;
            this.formData.promoCode = promoCode;
            
            Toast.success(`Áp dụng mã khuyến mãi thành công! Giảm ${utils.formatPrice(response.discount)}`);
            this.updateDisplay();
            
        } catch (error) {
            this.showFieldError(promoInput, error.message || 'Mã khuyến mãi không hợp lệ');
        }
    }

    removePromoCode() {
        this.promoDiscount = 0;
        this.appliedPromo = '';
        this.formData.promoCode = '';
        
        const promoInput = utils.$('#promo-code');
        if (promoInput) {
            promoInput.value = '';
        }
        
        this.updateDisplay();
        Toast.info('Đã hủy mã khuyến mãi');
    }

    async handleSubmit() {
        if (this.isSubmitting) return;

        // Validate all fields
        const form = utils.$('#booking-form');
        const requiredInputs = form.querySelectorAll('input[required]');
        let isFormValid = true;

        requiredInputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            Toast.error('Vui lòng kiểm tra lại thông tin');
            return;
        }

        // Collect form data
        const formData = new FormData(form);
        this.formData = {
            customerName: formData.get('customerName'),
            customerPhone: formData.get('customerPhone'),
            customerEmail: formData.get('customerEmail'),
            paymentMethod: formData.get('paymentMethod'),
            promoCode: this.appliedPromo
        };

        this.isSubmitting = true;
        this.updateDisplay();

        try {
            const bookingData = {
                showtimeId: this.options.showtimeId,
                seats: this.options.selectedSeats,
                totalPrice: (this.options.totalPrice - this.promoDiscount).toString(),
                paymentMethod: this.formData.paymentMethod,
                customerInfo: {
                    name: this.formData.customerName,
                    phone: this.formData.customerPhone,
                    email: this.formData.customerEmail
                },
                promoCode: this.appliedPromo || undefined
            };

            const response = await api.post('/api/bookings', bookingData);
            
            Toast.success('Đặt vé thành công!');
            this.options.onSuccess(response);
            
        } catch (error) {
            Toast.error(error.message || 'Không thể đặt vé. Vui lòng thử lại.');
        } finally {
            this.isSubmitting = false;
            this.updateDisplay();
        }
    }

    updateDisplay() {
        // Re-render the form with updated data
        const container = utils.$('.booking-form-container');
        if (container) {
            container.innerHTML = this.render();
            this.setupEventHandlers();
        }
    }

    static create(containerId, options) {
        const container = utils.$(`#${containerId}`);
        if (!container) {
            console.error(`Container #${containerId} not found`);
            return null;
        }

        const bookingForm = new BookingForm(options);
        container.innerHTML = bookingForm.render();
        bookingForm.setupEventHandlers();
        
        return bookingForm;
    }
}

// Export to global scope
window.BookingForm = BookingForm;
