# NaCinema Environment Variables

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/nacinema?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long

# Server Configuration
NODE_ENV=development
PORT=5000

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://localhost:6379

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# External APIs (Optional)
TMDB_API_KEY=your-tmdb-api-key
PAYMENT_GATEWAY_API_KEY=your-payment-api-key