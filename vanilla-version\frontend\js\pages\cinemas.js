// ===== CINEMAS PAGE =====

window.pages.cinemas = {
    cinemas: [],
    selectedCinema: null,
    currentCity: 'all',

    async render() {
        const mainContent = utils.$('#main-content');
        
        try {
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="page-loading">
                        <div class="spinner"></div>
                        <p><PERSON>ang tải danh sách rạp chiếu...</p>
                    </div>
                </div>
            `;

            // Load cinemas
            await this.loadCinemas();
            
            // Render page
            this.renderCinemasPage();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to render cinemas page:', error);
            this.renderError('Có lỗi xảy ra khi tải danh sách rạp chiếu.');
        }
    },

    async loadCinemas() {
        try {
            this.cinemas = await api.get('/api/cinemas');
        } catch (error) {
            console.error('Failed to load cinemas:', error);
            this.cinemas = [];
        }
    },

    renderCinemasPage() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="cinemas-page">
                <!-- Hero Section -->
                <div class="hero-section">
                    <div class="hero-background">
                        <div class="hero-overlay"></div>
                    </div>
                    <div class="container">
                        <div class="hero-content">
                            <h1 class="hero-title">
                                <i class="fas fa-building"></i>
                                Hệ thống rạp chiếu
                            </h1>
                            <p class="hero-subtitle">
                                Khám phá các rạp chiếu phim hiện đại với công nghệ âm thanh và hình ảnh tốt nhất
                            </p>
                            <div class="hero-stats">
                                <div class="stat-item">
                                    <span class="stat-number">${this.cinemas.length}</span>
                                    <span class="stat-label">Rạp chiếu</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">${this.getTotalRooms()}</span>
                                    <span class="stat-label">Phòng chiếu</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">${this.getUniqueCities().length}</span>
                                    <span class="stat-label">Thành phố</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="filters-section">
                    <div class="container">
                        <div class="filters-header">
                            <h2>Chọn thành phố</h2>
                            <div class="search-box">
                                <input type="text" class="form-input" placeholder="Tìm kiếm rạp..." id="cinema-search">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        
                        <div class="city-filters">
                            <button class="filter-btn ${this.currentCity === 'all' ? 'active' : ''}" 
                                    data-city="all">
                                Tất cả
                            </button>
                            ${this.getUniqueCities().map(city => `
                                <button class="filter-btn ${this.currentCity === city ? 'active' : ''}" 
                                        data-city="${city}">
                                    ${city}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <!-- Cinemas Grid -->
                <div class="cinemas-section">
                    <div class="container">
                        ${this.getFilteredCinemas().length > 0 ? `
                            <div class="cinemas-grid">
                                ${this.getFilteredCinemas().map(cinema => this.renderCinemaCard(cinema)).join('')}
                            </div>
                        ` : `
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <h3>Không tìm thấy rạp chiếu</h3>
                                <p>Không có rạp chiếu nào trong khu vực này.</p>
                                <button class="btn btn-primary" onclick="window.pages.cinemas.filterByCity('all')">
                                    Xem tất cả rạp
                                </button>
                            </div>
                        `}
                    </div>
                </div>

                <!-- Features Section -->
                <div class="features-section">
                    <div class="container">
                        <h2 class="section-title">Tiện ích tại rạp</h2>
                        <div class="features-grid">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-volume-up"></i>
                                </div>
                                <h3>Âm thanh Dolby Atmos</h3>
                                <p>Trải nghiệm âm thanh vòm 360 độ chân thực</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <h3>Màn hình IMAX</h3>
                                <p>Hình ảnh siêu nét với công nghệ IMAX tiên tiến</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-chair"></i>
                                </div>
                                <h3>Ghế VIP</h3>
                                <p>Ghế da cao cấp có thể ngả và massage</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-utensils"></i>
                                </div>
                                <h3>Dịch vụ ăn uống</h3>
                                <p>Bắp rang, nước ngọt và đồ ăn nhẹ</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-parking"></i>
                                </div>
                                <h3>Bãi đỗ xe</h3>
                                <p>Bãi đỗ xe rộng rãi và an toàn</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-wifi"></i>
                                </div>
                                <h3>WiFi miễn phí</h3>
                                <p>Kết nối internet tốc độ cao</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderCinemaCard(cinema) {
        return `
            <div class="cinema-card" data-cinema-id="${cinema.id}">
                <div class="cinema-header">
                    <div class="cinema-image">
                        <img src="${cinema.image || '/assets/images/cinema-default.jpg'}" alt="${cinema.name}">
                        <div class="cinema-status ${cinema.status}">
                            ${cinema.status === 'active' ? 'Đang hoạt động' : 'Tạm đóng cửa'}
                        </div>
                    </div>
                </div>
                
                <div class="cinema-content">
                    <h3 class="cinema-name">${cinema.name}</h3>
                    <div class="cinema-info">
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${cinema.address}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <span>${cinema.phone}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-door-open"></i>
                            <span>${cinema.rooms?.length || 0} phòng chiếu</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>Mở cửa: ${cinema.openTime || '08:00'} - ${cinema.closeTime || '23:00'}</span>
                        </div>
                    </div>
                    
                    ${cinema.features && cinema.features.length > 0 ? `
                        <div class="cinema-features">
                            <h4>Tiện ích:</h4>
                            <div class="features-list">
                                ${cinema.features.map(feature => `
                                    <span class="feature-tag">${feature}</span>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
                
                <div class="cinema-actions">
                    <button class="btn btn-primary" onclick="window.pages.cinemas.viewCinemaDetail(${cinema.id})">
                        <i class="fas fa-eye"></i>
                        Xem chi tiết
                    </button>
                    <button class="btn btn-secondary" onclick="window.pages.cinemas.viewMovies(${cinema.id})">
                        <i class="fas fa-film"></i>
                        Phim đang chiếu
                    </button>
                    <button class="btn btn-outline" onclick="window.pages.cinemas.getDirections(${cinema.id})">
                        <i class="fas fa-directions"></i>
                        Chỉ đường
                    </button>
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // City filter buttons
        const filterBtns = utils.$$('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const city = e.target.dataset.city;
                this.filterByCity(city);
            });
        });

        // Search functionality
        const searchInput = utils.$('#cinema-search');
        if (searchInput) {
            searchInput.addEventListener('input', utils.debounce(() => {
                this.searchCinemas();
            }, 300));
        }

        // Cinema card hover effects
        const cinemaCards = utils.$$('.cinema-card');
        cinemaCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('hovered');
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('hovered');
            });
        });
    },

    filterByCity(city) {
        this.currentCity = city;
        this.renderCinemasPage();
        this.setupEventHandlers();
    },

    searchCinemas() {
        const searchTerm = utils.$('#cinema-search').value.toLowerCase();
        // Filter and re-render based on search term
        this.renderCinemasPage();
        this.setupEventHandlers();
    },

    getFilteredCinemas() {
        let filtered = [...this.cinemas];
        
        // Filter by city
        if (this.currentCity !== 'all') {
            filtered = filtered.filter(cinema => 
                cinema.city === this.currentCity || 
                cinema.address.includes(this.currentCity)
            );
        }
        
        // Filter by search term
        const searchTerm = utils.$('#cinema-search')?.value?.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(cinema => 
                cinema.name.toLowerCase().includes(searchTerm) ||
                cinema.address.toLowerCase().includes(searchTerm)
            );
        }
        
        return filtered;
    },

    getUniqueCities() {
        const cities = [...new Set(this.cinemas.map(cinema => 
            cinema.city || this.extractCityFromAddress(cinema.address)
        ))];
        return cities.filter(city => city).sort();
    },

    extractCityFromAddress(address) {
        // Simple city extraction from address
        const parts = address.split(',');
        return parts[parts.length - 1]?.trim() || '';
    },

    getTotalRooms() {
        return this.cinemas.reduce((total, cinema) => 
            total + (cinema.rooms?.length || 0), 0
        );
    },

    async viewCinemaDetail(cinemaId) {
        try {
            const cinema = this.cinemas.find(c => c.id === cinemaId);
            if (!cinema) return;

            const modalContent = `
                <div class="cinema-detail-modal">
                    <div class="cinema-detail-header">
                        <img src="${cinema.image || '/assets/images/cinema-default.jpg'}" alt="${cinema.name}" class="cinema-detail-image">
                        <div class="cinema-detail-info">
                            <h2>${cinema.name}</h2>
                            <p class="cinema-description">${cinema.description || 'Rạp chiếu phim hiện đại với công nghệ tiên tiến.'}</p>
                        </div>
                    </div>
                    
                    <div class="cinema-detail-content">
                        <div class="detail-section">
                            <h3><i class="fas fa-info-circle"></i> Thông tin liên hệ</h3>
                            <div class="contact-info">
                                <p><i class="fas fa-map-marker-alt"></i> ${cinema.address}</p>
                                <p><i class="fas fa-phone"></i> ${cinema.phone}</p>
                                <p><i class="fas fa-envelope"></i> ${cinema.email || '<EMAIL>'}</p>
                                <p><i class="fas fa-clock"></i> ${cinema.openTime || '08:00'} - ${cinema.closeTime || '23:00'}</p>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h3><i class="fas fa-door-open"></i> Phòng chiếu</h3>
                            <div class="rooms-grid">
                                ${(cinema.rooms || []).map(room => `
                                    <div class="room-card">
                                        <h4>${room.name}</h4>
                                        <p>${room.capacity} ghế</p>
                                        <span class="room-type">${room.type || 'Standard'}</span>
                                    </div>
                                `).join('') || '<p>Thông tin phòng chiếu sẽ được cập nhật sớm.</p>'}
                            </div>
                        </div>
                        
                        ${cinema.features && cinema.features.length > 0 ? `
                            <div class="detail-section">
                                <h3><i class="fas fa-star"></i> Tiện ích</h3>
                                <div class="features-grid">
                                    ${cinema.features.map(feature => `
                                        <span class="feature-badge">${feature}</span>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            await Modal.show(modalContent, {
                title: 'Chi tiết rạp chiếu',
                size: 'large',
                showConfirm: false,
                cancelText: 'Đóng'
            });

        } catch (error) {
            Toast.error('Không thể tải thông tin rạp chiếu.');
        }
    },

    viewMovies(cinemaId) {
        // Navigate to home page with cinema filter
        window.router.navigate(`/?cinema=${cinemaId}`);
    },

    getDirections(cinemaId) {
        const cinema = this.cinemas.find(c => c.id === cinemaId);
        if (!cinema) return;

        // Open Google Maps with directions
        const address = encodeURIComponent(cinema.address);
        const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${address}`;
        window.open(mapsUrl, '_blank');
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải trang</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.pages.cinemas.render()">
                                Thử lại
                            </button>
                            <a href="#" class="btn btn-secondary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
