{"version": "0.2.0", "configurations": [{"name": "Launch Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/index.ts", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "env": {"NODE_ENV": "development"}, "runtimeArgs": ["-r", "tsx/cjs"], "console": "integratedTerminal", "restart": true, "protocol": "inspector"}, {"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/index.ts", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "env": {"NODE_ENV": "development", "DEBUG": "true"}, "runtimeArgs": ["-r", "tsx/cjs"], "console": "integratedTerminal", "restart": true, "protocol": "inspector", "sourceMaps": true}]}