# 🔄 Hướng dẫn chuyển đổi React sang HTML/CSS/JavaScript thuần

## 🎯 Tổng quan

Tôi đã tạo cấu trúc cơ bản để chuyển đổi dự án NaCinema từ React sang HTML/CSS/JavaScript thuần. Dưới đây là những gì đã hoàn thành và những gì cần làm tiếp.

## ✅ Đã hoàn thành

### 1. **Cấu trúc HTML cơ bản** (`index.html`)
- ✅ Layout chính với navigation, main content, footer
- ✅ Modal overlay và toast notifications
- ✅ Responsive design structure
- ✅ Font Awesome icons và Google Fonts

### 2. **CSS System** 
- ✅ `main.css` - CSS variables, utility classes, base styles
- ✅ `components.css` - Component-specific styles
- ✅ `responsive.css` - Responsive design cho tất cả devices
- ✅ Thay thế hoàn toàn Tailwind CSS

### 3. **JavaScript Core Systems**
- ✅ `utils.js` - Utility functions (DOM, formatting, validation, etc.)
- ✅ `api.js` - API client với caching (thay thế React Query)
- ✅ `auth.js` - Authentication system với JWT
- ✅ `router.js` - Client-side routing (thay thế Wouter)

### 4. **State Management**
- ✅ Global state manager
- ✅ Query cache system
- ✅ Authentication state management

## 🚧 Cần hoàn thành

### 1. **Navigation Component** (`js/components/navigation.js`)
```javascript
// Cần tạo file này để handle:
// - Mobile menu toggle
// - Search functionality  
// - User dropdown
// - Active link highlighting
```

### 2. **Modal System** (`js/components/modal.js`)
```javascript
// Cần tạo để handle:
// - Show/hide modals
// - Modal content management
// - Backdrop click handling
```

### 3. **Toast Notifications** (`js/components/toast.js`)
```javascript
// Cần tạo để handle:
// - Success/error/warning messages
// - Auto-dismiss functionality
// - Multiple toast management
```

### 4. **Page Components**
Cần tạo các file trong `js/pages/`:

#### `home.js` - Trang chủ
```javascript
window.pages.home = {
    async render() {
        // Load movies from API
        // Render movie grid
        // Setup filters
        // Handle search
    }
};
```

#### `movie-detail.js` - Chi tiết phim
```javascript
window.pages.movieDetail = {
    async render(movieId) {
        // Load movie details
        // Load showtimes
        // Render movie info
        // Setup booking buttons
    }
};
```

#### `login.js` & `register.js` - Authentication
```javascript
window.pages.login = {
    async render() {
        // Render login form
        // Handle form submission
        // Form validation
        // Redirect after login
    }
};
```

### 5. **SeatMap Component** (Quan trọng nhất)
Cần tạo `js/components/seatmap.js`:
```javascript
class SeatMap {
    constructor(container, showtimeId) {
        this.container = container;
        this.showtimeId = showtimeId;
        this.selectedSeats = [];
        this.seatData = null;
    }

    async render() {
        // Load seat layout
        // Render seat grid
        // Setup click handlers
        // Real-time updates
    }

    handleSeatClick(seatId) {
        // Toggle seat selection
        // Update pricing
        // Validate selection
    }
}
```

### 6. **Booking Flow**
Cần tạo `js/pages/booking.js`:
```javascript
window.pages.booking = {
    async render(showtimeId) {
        // Load showtime details
        // Render seat map
        // Render booking form
        // Handle payment
    }
};
```

## 📋 Kế hoạch hoàn thành

### Phase 1: Core Components (1-2 ngày)
1. **Navigation component** - Mobile menu, search, user dropdown
2. **Modal system** - Reusable modal component
3. **Toast notifications** - Success/error messages

### Phase 2: Main Pages (2-3 ngày)
1. **Home page** - Movie grid, filters, search
2. **Movie detail page** - Movie info, showtimes
3. **Authentication pages** - Login/register forms

### Phase 3: Booking System (2-3 ngày)
1. **SeatMap component** - Interactive seat selection
2. **Booking flow** - Complete booking process
3. **Payment integration** - Multiple payment methods

### Phase 4: Admin & Polish (1-2 ngày)
1. **Admin panel** - Movie/user management
2. **User dashboard** - Booking history
3. **Testing & optimization**

## 🛠️ Cách sử dụng

### 1. **Cấu trúc thư mục**
```
vanilla-version/
├── index.html          # Entry point
├── css/
│   ├── main.css        # Base styles
│   ├── components.css  # Component styles
│   └── responsive.css  # Responsive design
├── js/
│   ├── utils.js        # Utilities
│   ├── api.js          # API client
│   ├── auth.js         # Authentication
│   ├── router.js       # Routing
│   ├── app.js          # Main app
│   ├── components/     # UI components
│   └── pages/          # Page components
└── assets/             # Images, icons
```

### 2. **Khởi chạy**
1. Copy thư mục `vanilla-version` 
2. Chạy server backend (giữ nguyên)
3. Serve static files từ `vanilla-version/`
4. Truy cập `index.html`

### 3. **Development workflow**
1. Tạo từng component trong `js/components/`
2. Tạo từng page trong `js/pages/`
3. Test từng tính năng riêng biệt
4. Tích hợp và test toàn bộ

## 🎨 Styling Guidelines

### CSS Variables đã định nghĩa:
```css
--primary-red: #dc2626;
--gray-900: #111827;
--spacing-4: 1rem;
--font-size-base: 1rem;
--radius-md: 0.375rem;
--transition-fast: 150ms ease-in-out;
```

### Utility Classes có sẵn:
```css
.container, .flex, .items-center, .justify-between
.btn, .btn-primary, .btn-secondary
.card, .badge, .form-input
.fade-in, .slide-in-up
```

## 🔌 API Integration

### Sử dụng API client:
```javascript
// Get movies
const movies = await movieAPI.getMovies({ status: 'active' });

// Create booking
const booking = await bookingAPI.createBooking(bookingData);

// With caching
const cachedMovies = await queryCache.query('movies', 
    () => movieAPI.getMovies()
);
```

### Authentication:
```javascript
// Login
await auth.login({ username, password });

// Check auth
if (auth.isAuthenticated()) {
    // User is logged in
}

// Check permissions
if (auth.hasRole('admin')) {
    // Show admin features
}
```

## 🚀 Next Steps

1. **Bắt đầu với Navigation component** - Tạo `js/components/navigation.js`
2. **Tạo Home page** - Tạo `js/pages/home.js` 
3. **Test từng component** - Đảm bảo hoạt động đúng
4. **Tích hợp dần dần** - Kết nối các components

Bạn có muốn tôi tiếp tục tạo một component cụ thể nào không? Tôi có thể bắt đầu với Navigation component hoặc Home page để bạn thấy cách hoạt động.
