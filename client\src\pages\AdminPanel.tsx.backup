import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { MovieWithDetails } from "@/lib/types";
import { Plus, Edit, Trash2, Users, Calendar, BarChart3, Film, Building, Ticket, DollarSign, Star, TrendingUp, Phone } from "lucide-react";

const movieSchema = z.object({
  title: z.string().min(1, "Tên phim là bắt buộc"),
  description: z.string().min(1, "Mô tả là bắt buộc"),
  genre: z.string().min(1, "Thể loại là bắt buộc"),
  duration: z.number().min(1, "Thời lượng phải lớn hơn 0"),
  ageRating: z.string().min(1, "Giới hạn tuổi là bắt buộc"),
  posterUrl: z.string().optional(),
  trailerUrl: z.string().optional(),
  director: z.string().optional(),
  actors: z.string().optional(),
  status: z.string().default("active"),
});

const cinemaSchema = z.object({
  name: z.string().min(1, "Tên rạp là bắt buộc"),
  address: z.string().min(1, "Địa chỉ là bắt buộc"),
  phone: z.string().optional(),
});

type MovieForm = z.infer<typeof movieSchema>;
type CinemaForm = z.infer<typeof cinemaSchema>;

export default function AdminPanel() {
  const [activeTab, setActiveTab] = useState("statistics");
  const [isMovieDialogOpen, setIsMovieDialogOpen] = useState(false);
  const [isCinemaDialogOpen, setIsCinemaDialogOpen] = useState(false);
  const [editingMovie, setEditingMovie] = useState<MovieWithDetails | null>(null);
  const [editingCinema, setEditingCinema] = useState<any>(null);
  const { toast } = useToast();

  const { data: movies, isLoading: moviesLoading } = useQuery<MovieWithDetails[]>({
    queryKey: ["/api/movies"],
  });

  const { data: cinemas, isLoading: cinemasLoading } = useQuery({
    queryKey: ["/api/cinemas"],
  });

  const { data: tickets } = useQuery({
    queryKey: ["/api/admin/tickets"],
  });

  const { data: promotions } = useQuery({
    queryKey: ["/api/promotions"],
  });

  const { data: reviews } = useQuery({
    queryKey: ["/api/reviews"],
  });

  const movieForm = useForm<MovieForm>({
    resolver: zodResolver(movieSchema),
    defaultValues: {
      title: "",
      description: "",
      genre: "",
      duration: 0,
      ageRating: "",
      posterUrl: "",
      trailerUrl: "",
      director: "",
      actors: "",
      status: "active",
    },
  });

  const cinemaForm = useForm<CinemaForm>({
    resolver: zodResolver(cinemaSchema),
    defaultValues: {
      name: "",
      address: "",
      phone: "",
    },
  });

  const createMovieMutation = useMutation({
    mutationFn: async (data: MovieForm) => {
      const movieData = {
        ...data,
        actors: data.actors ? data.actors.split(",").map(actor => actor.trim()) : [],
        releaseDate: new Date(),
      };
      const response = await apiRequest("POST", "/api/movies", movieData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/movies"] });
      setIsMovieDialogOpen(false);
      movieForm.reset();
      toast({
        title: "Thành công",
        description: "Phim đã được thêm thành công",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Lỗi",
        description: error.message || "Không thể thêm phim",
        variant: "destructive",
      });
    },
  });

  const updateMovieMutation = useMutation({
    mutationFn: async (data: MovieForm & { id: number }) => {
      const { id, ...updateData } = data;
      const movieData = {
        ...updateData,
        actors: updateData.actors ? updateData.actors.split(",").map(actor => actor.trim()) : [],
      };
      const response = await apiRequest("PUT", `/api/movies/${id}`, movieData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/movies"] });
      setIsMovieDialogOpen(false);
      setEditingMovie(null);
      movieForm.reset();
      toast({
        title: "Thành công",
        description: "Phim đã được cập nhật thành công",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Lỗi",
        description: error.message || "Không thể cập nhật phim",
        variant: "destructive",
      });
    },
  });

  const deleteMovieMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/movies/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/movies"] });
      toast({
        title: "Thành công",
        description: "Phim đã được xóa thành công",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Lỗi",
        description: error.message || "Không thể xóa phim",
        variant: "destructive",
      });
    },
  });

  const createCinemaMutation = useMutation({
    mutationFn: async (data: CinemaForm) => {
      const response = await apiRequest("POST", "/api/cinemas", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cinemas"] });
      setIsCinemaDialogOpen(false);
      cinemaForm.reset();
      toast({
        title: "Thành công",
        description: "Rạp chiếu đã được thêm thành công",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Lỗi",
        description: error.message || "Không thể thêm rạp chiếu",
        variant: "destructive",
      });
    },
  });

  const handleEditMovie = (movie: MovieWithDetails) => {
    setEditingMovie(movie);
    movieForm.reset({
      title: movie.title,
      description: movie.description,
      genre: movie.genre,
      duration: movie.duration,
      ageRating: movie.ageRating,
      posterUrl: movie.posterUrl || "",
      trailerUrl: movie.trailerUrl || "",
      director: movie.director || "",
      actors: movie.actors?.join(", ") || "",
      status: movie.status,
    });
    setIsMovieDialogOpen(true);
  };

  const handleDeleteMovie = (id: number) => {
    if (confirm("Bạn có chắc chắn muốn xóa phim này?")) {
      deleteMovieMutation.mutate(id);
    }
  };

  const onSubmitMovie = (data: MovieForm) => {
    if (editingMovie) {
      updateMovieMutation.mutate({ ...data, id: editingMovie.id });
    } else {
      createMovieMutation.mutate(data);
    }
  };

  const onSubmitCinema = (data: CinemaForm) => {
    createCinemaMutation.mutate(data);
  };

  // Calculate statistics
  const calculateStats = () => {
    const totalMovies = movies?.length || 0;
    const totalCinemas = cinemas?.length || 0;
    const totalTickets = tickets?.length || 0;
    const totalRevenue = tickets?.reduce((sum: number, ticket: any) => 
      sum + parseFloat(ticket.totalPrice || "0"), 0) || 0;
    const totalReviews = reviews?.length || 0;
    const avgRating = reviews?.length ? 
      (reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / reviews.length).toFixed(1) : "0";

    return {
      totalMovies,
      totalCinemas,
      totalTickets,
      totalRevenue,
      totalReviews,
      avgRating
    };
  };

  const stats = calculateStats();

  return (
    <div className="pt-24 pb-16">
      <div className="max-w-7xl mx-auto px-4">
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white text-2xl">Quản trị hệ thống</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 bg-gray-700">
                <TabsTrigger value="statistics" className="text-white">Thống kê</TabsTrigger>
                <TabsTrigger value="management" className="text-white">Quản lý</TabsTrigger>
                <TabsTrigger value="users" className="text-white">Người dùng</TabsTrigger>
              </TabsList>

              <TabsContent value="statistics" className="mt-6">
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white mb-6">Thống kê hệ thống</h3>
                  
                  {/* Statistics Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="bg-gray-800 border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-400 text-sm">Tổng số phim</p>
                            <p className="text-2xl font-bold text-white">{stats.totalMovies}</p>
                          </div>
                          <div className="bg-blue-600 p-3 rounded-full">
                            <Film className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-400 text-sm">Tổng số rạp</p>
                            <p className="text-2xl font-bold text-white">{stats.totalCinemas}</p>
                          </div>
                          <div className="bg-green-600 p-3 rounded-full">
                            <Building className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-400 text-sm">Vé đã bán</p>
                            <p className="text-2xl font-bold text-white">{stats.totalTickets}</p>
                          </div>
                          <div className="bg-yellow-600 p-3 rounded-full">
                            <Ticket className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-400 text-sm">Doanh thu</p>
                            <p className="text-2xl font-bold text-white">
                              {new Intl.NumberFormat('vi-VN', {
                                style: 'currency',
                                currency: 'VND'
                              }).format(stats.totalRevenue)}
                            </p>
                          </div>
                          <div className="bg-red-600 p-3 rounded-full">
                            <DollarSign className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-400 text-sm">Đánh giá</p>
                            <p className="text-2xl font-bold text-white">{stats.totalReviews}</p>
                          </div>
                          <div className="bg-purple-600 p-3 rounded-full">
                            <Star className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-400 text-sm">Điểm trung bình</p>
                            <p className="text-2xl font-bold text-white">{stats.avgRating}/5</p>
                          </div>
                          <div className="bg-orange-600 p-3 rounded-full">
                            <TrendingUp className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Recent Activities */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="bg-gray-800 border-gray-700">
                      <CardHeader>
                        <CardTitle className="text-white">Phim mới nhất</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {movies?.slice(0, 5).map((movie) => (
                            <div key={movie.id} className="flex items-center justify-between">
                              <div>
                                <p className="text-white font-medium">{movie.title}</p>
                                <p className="text-gray-400 text-sm">{movie.genre}</p>
                              </div>
                              <Badge variant={movie.status === 'active' ? 'default' : 'secondary'}>
                                {movie.status === 'active' ? 'Đang chiếu' : 'Sắp chiếu'}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700">
                      <CardHeader>
                        <CardTitle className="text-white">Khuyến mãi đang hoạt động</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {promotions?.filter((promo: any) => promo.status === 'active').slice(0, 5).map((promo: any) => (
                            <div key={promo.id} className="flex items-center justify-between">
                              <div>
                                <p className="text-white font-medium">{promo.title}</p>
                                <p className="text-gray-400 text-sm">{promo.description}</p>
                              </div>
                              <Badge className="bg-green-600">
                                -{promo.discountValue}{promo.discountType === 'percentage' ? '%' : 'đ'}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="management" className="mt-6">
                <div className="space-y-8">
                  {/* Movies Management Section */}
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-semibold text-white">Quản lý phim</h3>
                      <Dialog open={isMovieDialogOpen} onOpenChange={setIsMovieDialogOpen}>
                        <DialogTrigger asChild>
                          <Button className="bg-red-600 hover:bg-red-700">
                            <Plus className="mr-2" size={16} />
                            Thêm phim mới
                          </Button>
                        </DialogTrigger>
                      <DialogContent className="max-w-2xl bg-gray-800 border-gray-700">
                        <DialogHeader>
                          <DialogTitle className="text-white">
                            {editingMovie ? "Chỉnh sửa phim" : "Thêm phim mới"}
                          </DialogTitle>
                        </DialogHeader>
                        <Form {...movieForm}>
                          <form onSubmit={movieForm.handleSubmit(onSubmitMovie)} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={movieForm.control}
                                name="title"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">Tên phim</FormLabel>
                                    <FormControl>
                                      <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={movieForm.control}
                                name="genre"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">Thể loại</FormLabel>
                                    <FormControl>
                                      <Select value={field.value} onValueChange={field.onChange}>
                                        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="Hành động">Hành động</SelectItem>
                                          <SelectItem value="Tâm lý">Tâm lý</SelectItem>
                                          <SelectItem value="Kinh dị">Kinh dị</SelectItem>
                                          <SelectItem value="Hài">Hài</SelectItem>
                                          <SelectItem value="Tình cảm">Tình cảm</SelectItem>
                                          <SelectItem value="Khoa học viễn tưởng">Khoa học viễn tưởng</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <FormField
                              control={movieForm.control}
                              name="description"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">Mô tả</FormLabel>
                                  <FormControl>
                                    <Textarea {...field} className="bg-gray-700 border-gray-600 text-white" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={movieForm.control}
                                name="duration"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">Thời lượng (phút)</FormLabel>
                                    <FormControl>
                                      <Input 
                                        {...field} 
                                        type="number" 
                                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                                        className="bg-gray-700 border-gray-600 text-white" 
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={movieForm.control}
                                name="ageRating"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">Giới hạn tuổi</FormLabel>
                                    <FormControl>
                                      <Select value={field.value} onValueChange={field.onChange}>
                                        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="K">K</SelectItem>
                                          <SelectItem value="13+">13+</SelectItem>
                                          <SelectItem value="16+">16+</SelectItem>
                                          <SelectItem value="18+">18+</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={movieForm.control}
                                name="posterUrl"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">URL Poster</FormLabel>
                                    <FormControl>
                                      <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={movieForm.control}
                                name="trailerUrl"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">URL Trailer</FormLabel>
                                    <FormControl>
                                      <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={movieForm.control}
                                name="director"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">Đạo diễn</FormLabel>
                                    <FormControl>
                                      <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={movieForm.control}
                                name="actors"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">Diễn viên (cách nhau bằng dấu phẩy)</FormLabel>
                                    <FormControl>
                                      <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex space-x-4">
                              <Button 
                                type="submit" 
                                className="bg-red-600 hover:bg-red-700"
                                disabled={createMovieMutation.isPending || updateMovieMutation.isPending}
                              >
                                {editingMovie ? "Cập nhật" : "Thêm phim"}
                              </Button>
                              <Button 
                                type="button" 
                                variant="outline" 
                                onClick={() => {
                                  setIsMovieDialogOpen(false);
                                  setEditingMovie(null);
                                  movieForm.reset();
                                }}
                                className="border-gray-600 text-gray-300"
                              >
                                Hủy
                              </Button>
                            </div>
                          </form>
                        </Form>
                      </DialogContent>
                    </Dialog>
                  </div>

                  <div className="space-y-4">
                    {moviesLoading ? (
                      Array.from({ length: 3 }).map((_, index) => (
                        <Card key={index} className="bg-gray-800 border-gray-700">
                          <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                              <Skeleton className="w-16 h-20 rounded" />
                              <div className="flex-1 space-y-2">
                                <Skeleton className="h-5 w-3/4" />
                                <Skeleton className="h-4 w-1/2" />
                                <Skeleton className="h-4 w-1/4" />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    ) : (
                      movies?.map((movie) => (
                        <Card key={movie.id} className="bg-gray-800 border-gray-700 hover:bg-gray-750 transition-colors">
                          <CardContent className="p-6">
                            <div className="flex flex-col lg:flex-row items-start gap-4">
                              <div className="flex items-center space-x-4 flex-1">
                                <img
                                  src={movie.posterUrl || "/placeholder-movie.jpg"}
                                  alt={movie.title}
                                  className="w-16 h-20 object-cover rounded flex-shrink-0"
                                />
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-semibold text-white text-lg truncate">{movie.title}</h4>
                                  <div className="flex flex-wrap gap-4 mt-2 text-sm text-gray-400">
                                    <span>Thể loại: {movie.genre}</span>
                                    <span>Thời lượng: {movie.duration} phút</span>
                                    <span>Giới hạn: {movie.ageRating}</span>
                                    <span>Năm: {movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : "2024"}</span>
                                  </div>
                                  <p className="text-gray-400 text-sm mt-2 line-clamp-2">{movie.description}</p>
                                </div>
                              </div>
                              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-3 lg:gap-4">
                                <Badge className={movie.status === "active" ? "bg-green-600" : "bg-red-600"}>
                                  {movie.status === "active" ? "Đang chiếu" : "Ngừng chiếu"}
                                </Badge>
                                <div className="flex space-x-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleEditMovie(movie)}
                                    className="border-gray-600 text-blue-400 hover:bg-blue-600 hover:text-white"
                                  >
                                    <Edit size={16} />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeleteMovie(movie.id)}
                                    className="border-gray-600 text-red-400 hover:bg-red-600 hover:text-white"
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                  
                  {/* Cinemas Management Section */}
                  <div className="space-y-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                      <h3 className="text-xl font-semibold text-white">Quản lý rạp chiếu</h3>
                      <Dialog open={isCinemaDialogOpen} onOpenChange={setIsCinemaDialogOpen}>
                        <DialogTrigger asChild>
                          <Button className="bg-red-600 hover:bg-red-700 w-full sm:w-auto">
                            <Plus className="mr-2" size={16} />
                            Thêm rạp mới
                          </Button>
                        </DialogTrigger>
                      <DialogContent className="bg-gray-800 border-gray-700">
                        <DialogHeader>
                          <DialogTitle className="text-white">Thêm rạp chiếu mới</DialogTitle>
                        </DialogHeader>
                        <Form {...cinemaForm}>
                          <form onSubmit={cinemaForm.handleSubmit(onSubmitCinema)} className="space-y-4">
                            <FormField
                              control={cinemaForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">Tên rạp</FormLabel>
                                  <FormControl>
                                    <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={cinemaForm.control}
                              name="address"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">Địa chỉ</FormLabel>
                                  <FormControl>
                                    <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={cinemaForm.control}
                              name="phone"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">Số điện thoại</FormLabel>
                                  <FormControl>
                                    <Input {...field} className="bg-gray-700 border-gray-600 text-white" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <div className="flex space-x-4">
                              <Button 
                                type="submit" 
                                className="bg-red-600 hover:bg-red-700"
                                disabled={createCinemaMutation.isPending}
                              >
                                Thêm rạp
                              </Button>
                              <Button 
                                type="button" 
                                variant="outline" 
                                onClick={() => {
                                  setIsCinemaDialogOpen(false);
                                  cinemaForm.reset();
                                }}
                                className="border-gray-600 text-gray-300"
                              >
                                Hủy
                              </Button>
                            </div>
                          </form>
                        </Form>
                      </DialogContent>
                    </Dialog>
                  </div>

                  <div className="space-y-4">
                    {cinemasLoading ? (
                      Array.from({ length: 3 }).map((_, index) => (
                        <Card key={index} className="bg-gray-800 border-gray-700">
                          <CardContent className="p-6">
                            <div className="space-y-2">
                              <Skeleton className="h-5 w-3/4" />
                              <Skeleton className="h-4 w-full" />
                              <Skeleton className="h-4 w-1/2" />
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    ) : (
                      cinemas?.map((cinema: any) => (
                        <Card key={cinema.id} className="bg-gray-800 border-gray-700 hover:bg-gray-750 transition-colors">
                          <CardContent className="p-6">
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                              <div className="flex-1">
                                <h4 className="text-white font-semibold text-lg">{cinema.name}</h4>
                                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 mt-2 text-sm text-gray-400">
                                  <span><Building className="inline w-4 h-4 mr-1" />Địa chỉ: {cinema.address}</span>
                                  <span><Phone className="inline w-4 h-4 mr-1" />SĐT: {cinema.phone || "Chưa cập nhật"}</span>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="border-gray-600 text-blue-400 hover:bg-blue-600 hover:text-white"
                                >
                                  <Edit size={16} />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="border-gray-600 text-red-400 hover:bg-red-600 hover:text-white"
                                >
                                  <Trash2 size={16} />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="users" className="mt-6">
                <Card className="bg-gray-900 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Users className="mr-2" />
                      Quản lý người dùng
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-400">
                      <Users size={48} className="mx-auto mb-4 text-gray-600" />
                      <p>Chức năng quản lý người dùng sẽ được triển khai sau</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="statistics" className="mt-6">
                <Card className="bg-gray-900 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <BarChart3 className="mr-2" />
                      Thống kê doanh thu
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      <Card className="bg-gray-800 border-gray-700">
                        <CardContent className="p-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-yellow-400 mb-2">
                              {tickets?.length || 0}
                            </div>
                            <div className="text-sm text-gray-400">Tổng số vé</div>
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gray-800 border-gray-700">
                        <CardContent className="p-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-400 mb-2">
                              {movies?.length || 0}
                            </div>
                            <div className="text-sm text-gray-400">Số phim</div>
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gray-800 border-gray-700">
                        <CardContent className="p-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-400 mb-2">
                              {cinemas?.length || 0}
                            </div>
                            <div className="text-sm text-gray-400">Số rạp</div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="text-center py-8 text-gray-400">
                      <BarChart3 size={48} className="mx-auto mb-4 text-gray-600" />
                      <p>Biểu đồ thống kê chi tiết sẽ được triển khai sau</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
