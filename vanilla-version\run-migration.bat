@echo off
echo ========================================
echo   NaCinema React to Vanilla Migration
echo ========================================
echo.

echo [INFO] Starting migration process...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo [INFO] Node.js detected: 
node --version

echo.
echo [INFO] Checking vanilla version...

REM Check if vanilla files exist
if not exist "index.html" (
    echo [ERROR] index.html not found in vanilla-version directory
    echo Please ensure you are in the correct directory
    pause
    exit /b 1
)

if not exist "js\app.js" (
    echo [ERROR] js\app.js not found
    echo Please ensure vanilla version is complete
    pause
    exit /b 1
)

echo [SUCCESS] Vanilla files found!
echo.

echo [INFO] Testing vanilla version...
echo Opening test page in browser...
echo Please test all features before continuing...
echo.

REM Open test page in browser
start "" "test-features.html"

echo.
echo ========================================
echo   IMPORTANT: Manual Testing Required
echo ========================================
echo.
echo 1. A test page has opened in your browser
echo 2. Please run all tests and verify they pass
echo 3. Test the main application functionality:
echo    - Navigation between pages
echo    - Movie browsing
echo    - User authentication
echo    - Booking flow (if backend is running)
echo    - Responsive design
echo.

set /p continue="Have you completed testing and everything works? (y/N): "
if /i not "%continue%"=="y" if /i not "%continue%"=="yes" (
    echo [INFO] Migration cancelled by user
    echo Please fix any issues and run this script again
    pause
    exit /b 0
)

echo.
echo [INFO] User confirmed tests passed
echo.

echo [INFO] Creating backup of React code...
echo This may take a few minutes...
echo.

REM Run the migration script
node migrate-to-vanilla.js

if %errorlevel% neq 0 (
    echo [ERROR] Migration script failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Migration Completed Successfully!
echo ========================================
echo.
echo [SUCCESS] React code has been backed up
echo [SUCCESS] Vanilla version moved to root directory
echo [SUCCESS] React files cleaned up
echo [SUCCESS] New package.json created
echo [SUCCESS] README.md updated
echo.
echo Next steps:
echo 1. cd .. (go to root directory)
echo 2. npm install (install new dependencies)
echo 3. npm run dev (start development server)
echo.
echo Your React code is safely backed up in react-backup-* folder
echo.

pause
