// ===== AUTHENTICATION SYSTEM =====

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.token = null;
        this.refreshTimer = null;
        this.listeners = [];
        
        // Initialize from localStorage
        this.init();
    }

    // Initialize auth state from localStorage
    init() {
        const token = utils.storage.get('authToken');
        const user = utils.storage.get('currentUser');
        
        if (token && user) {
            this.token = token;
            this.currentUser = user;
            this.setupTokenRefresh();
        }
    }

    // Login user
    async login(credentials) {
        try {
            const response = await authAPI.login(credentials);
            
            if (response.user && response.token) {
                this.setAuthData(response.user, response.token);
                this.notifyListeners('login', response.user);
                return response;
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    }

    // Register user
    async register(userData) {
        try {
            const response = await authAPI.register(userData);
            
            if (response.user && response.token) {
                this.setAuthData(response.user, response.token);
                this.notifyListeners('register', response.user);
                return response;
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('Registration failed:', error);
            throw error;
        }
    }

    // Logout user
    async logout() {
        try {
            // Call logout API
            await authAPI.logout();
        } catch (error) {
            console.warn('Logout API call failed:', error);
        } finally {
            // Clear local auth data regardless of API call result
            this.clearAuthData();
            this.notifyListeners('logout', null);
        }
    }

    // Set authentication data
    setAuthData(user, token) {
        this.currentUser = user;
        this.token = token;
        
        // Store in localStorage
        utils.storage.set('authToken', token);
        utils.storage.set('currentUser', user);
        
        // Setup token refresh
        this.setupTokenRefresh();
        
        // Clear API cache to refetch with new auth
        api.clearCache();
    }

    // Clear authentication data
    clearAuthData() {
        this.currentUser = null;
        this.token = null;
        
        // Clear localStorage
        utils.storage.remove('authToken');
        utils.storage.remove('currentUser');
        
        // Clear refresh timer
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }
        
        // Clear API cache
        api.clearCache();
    }

    // Setup automatic token refresh
    setupTokenRefresh() {
        if (!this.token) return;
        
        try {
            // Decode JWT to get expiration time
            const payload = JSON.parse(atob(this.token.split('.')[1]));
            const expirationTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();
            const timeUntilExpiry = expirationTime - currentTime;
            
            // Refresh token 5 minutes before expiry
            const refreshTime = Math.max(timeUntilExpiry - 300000, 60000); // At least 1 minute
            
            if (this.refreshTimer) {
                clearTimeout(this.refreshTimer);
            }
            
            this.refreshTimer = setTimeout(() => {
                this.refreshToken();
            }, refreshTime);
            
        } catch (error) {
            console.warn('Failed to setup token refresh:', error);
        }
    }

    // Refresh authentication token
    async refreshToken() {
        try {
            const response = await authAPI.refreshToken();
            
            if (response.token) {
                this.token = response.token;
                utils.storage.set('authToken', response.token);
                this.setupTokenRefresh();
                
                console.log('Token refreshed successfully');
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            // If refresh fails, logout user
            this.logout();
        }
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!(this.currentUser && this.token);
    }

    // Check if user has specific role
    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    // Check if user has any of the specified roles
    hasAnyRole(roles) {
        return this.currentUser && roles.includes(this.currentUser.role);
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        const rolePermissions = {
            user: ['view:movies', 'create:bookings', 'view:own_bookings'],
            staff: ['view:movies', 'create:bookings', 'view:own_bookings', 'view:all_bookings', 'manage:showtimes'],
            admin: ['*'] // Admin has all permissions
        };
        
        const userPermissions = rolePermissions[this.currentUser.role] || [];
        return userPermissions.includes('*') || userPermissions.includes(permission);
    }

    // Add auth state listener
    addListener(callback) {
        this.listeners.push(callback);
        
        // Return unsubscribe function
        return () => {
            this.listeners = this.listeners.filter(listener => listener !== callback);
        };
    }

    // Notify all listeners of auth state changes
    notifyListeners(event, user) {
        this.listeners.forEach(callback => {
            try {
                callback(event, user);
            } catch (error) {
                console.error('Auth listener error:', error);
            }
        });
    }

    // Get user initials for avatar
    getUserInitials() {
        if (!this.currentUser) return '';
        
        const name = this.currentUser.fullName || this.currentUser.username || '';
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .substring(0, 2);
    }

    // Update user profile
    async updateProfile(profileData) {
        try {
            const response = await api.put('/auth/profile', profileData);
            
            if (response.user) {
                this.currentUser = { ...this.currentUser, ...response.user };
                utils.storage.set('currentUser', this.currentUser);
                this.notifyListeners('profile_updated', this.currentUser);
            }
            
            return response;
        } catch (error) {
            console.error('Profile update failed:', error);
            throw error;
        }
    }

    // Change password
    async changePassword(passwordData) {
        try {
            const response = await api.put('/auth/password', passwordData);
            return response;
        } catch (error) {
            console.error('Password change failed:', error);
            throw error;
        }
    }
}

// ===== ROUTE PROTECTION =====

class RouteGuard {
    constructor(authManager) {
        this.auth = authManager;
    }

    // Check if route requires authentication
    requiresAuth(route) {
        const protectedRoutes = ['/dashboard', '/admin', '/profile', '/bookings'];
        return protectedRoutes.some(protected => route.startsWith(protected));
    }

    // Check if route requires specific role
    requiresRole(route) {
        const roleRoutes = {
            '/admin': ['admin'],
            '/staff': ['staff', 'admin']
        };
        
        for (const [routePattern, roles] of Object.entries(roleRoutes)) {
            if (route.startsWith(routePattern)) {
                return roles;
            }
        }
        
        return null;
    }

    // Check if user can access route
    canAccess(route) {
        // Check authentication requirement
        if (this.requiresAuth(route) && !this.auth.isAuthenticated()) {
            return { allowed: false, reason: 'authentication_required', redirectTo: '/login' };
        }

        // Check role requirement
        const requiredRoles = this.requiresRole(route);
        if (requiredRoles && !this.auth.hasAnyRole(requiredRoles)) {
            return { allowed: false, reason: 'insufficient_permissions', redirectTo: '/' };
        }

        return { allowed: true };
    }

    // Redirect to appropriate page based on access check
    handleRouteAccess(route) {
        const access = this.canAccess(route);
        
        if (!access.allowed) {
            if (access.redirectTo) {
                window.router.navigate(access.redirectTo);
            }
            return false;
        }
        
        return true;
    }
}

// Create global auth manager and route guard
const auth = new AuthManager();
const routeGuard = new RouteGuard(auth);

// ===== AUTH UI HELPERS =====

const authUI = {
    // Update navigation based on auth state
    updateNavigation() {
        const userMenu = utils.$('#user-menu');
        if (!userMenu) return;

        if (auth.isAuthenticated()) {
            const user = auth.getCurrentUser();
            const initials = auth.getUserInitials();
            
            userMenu.innerHTML = `
                <div class="user-avatar" id="user-avatar">${initials}</div>
                <div class="user-dropdown" id="user-dropdown">
                    <div class="user-dropdown-item">
                        <i class="fas fa-user"></i>
                        <span>${user.fullName || user.username}</span>
                    </div>
                    <div class="user-dropdown-divider"></div>
                    <a href="#" class="user-dropdown-item" data-route="/profile">
                        <i class="fas fa-cog"></i>
                        <span>Hồ sơ</span>
                    </a>
                    <a href="#" class="user-dropdown-item" data-route="/bookings">
                        <i class="fas fa-ticket-alt"></i>
                        <span>Vé của tôi</span>
                    </a>
                    ${auth.hasRole('admin') ? `
                        <a href="#" class="user-dropdown-item" data-route="/admin">
                            <i class="fas fa-shield-alt"></i>
                            <span>Quản trị</span>
                        </a>
                    ` : ''}
                    ${auth.hasAnyRole(['staff', 'admin']) ? `
                        <a href="#" class="user-dropdown-item" data-route="/staff">
                            <i class="fas fa-tools"></i>
                            <span>Nhân viên</span>
                        </a>
                    ` : ''}
                    <div class="user-dropdown-divider"></div>
                    <button class="user-dropdown-item" id="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Đăng xuất</span>
                    </button>
                </div>
            `;
            
            // Setup dropdown toggle
            const avatar = utils.$('#user-avatar');
            const dropdown = utils.$('#user-dropdown');
            
            avatar.addEventListener('click', (e) => {
                e.stopPropagation();
                dropdown.classList.toggle('show');
            });
            
            // Setup logout
            const logoutBtn = utils.$('#logout-btn');
            logoutBtn.addEventListener('click', async () => {
                try {
                    await auth.logout();
                    window.router.navigate('/');
                } catch (error) {
                    console.error('Logout error:', error);
                }
            });
            
        } else {
            userMenu.innerHTML = `
                <a href="#" class="btn btn-secondary btn-sm" data-route="/login">Đăng nhập</a>
                <a href="#" class="btn btn-primary btn-sm" data-route="/register">Đăng ký</a>
            `;
        }
    },

    // Close dropdowns when clicking outside
    setupGlobalClickHandler() {
        document.addEventListener('click', () => {
            const dropdown = utils.$('#user-dropdown');
            if (dropdown) {
                dropdown.classList.remove('show');
            }
        });
    }
};

// Listen for auth state changes
auth.addListener((event, user) => {
    authUI.updateNavigation();
    
    // Update global state
    state.setState('currentUser', user);
    state.setState('isAuthenticated', !!user);
});

// Export to global scope
window.auth = auth;
window.routeGuard = routeGuard;
window.authUI = authUI;
