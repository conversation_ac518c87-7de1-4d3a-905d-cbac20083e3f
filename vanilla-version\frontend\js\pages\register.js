// ===== REGISTER PAGE =====

window.pages.register = {
    async render() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="auth-page">
                <div class="auth-container">
                    <div class="auth-card">
                        <div class="auth-header">
                            <div class="auth-logo">
                                <i class="fas fa-film"></i>
                                <span>NaCinema</span>
                            </div>
                            <h2>Đăng ký tài khoản</h2>
                            <p>Tạo tài khoản để trải nghiệm đầy đủ dịch vụ</p>
                        </div>

                        <form id="register-form" class="auth-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label" for="firstName">Họ</label>
                                    <input type="text" 
                                           id="firstName" 
                                           name="firstName" 
                                           class="form-input" 
                                           placeholder="Nhập họ"
                                           required>
                                    <div class="form-error" id="firstName-error"></div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="lastName">Tên</label>
                                    <input type="text" 
                                           id="lastName" 
                                           name="lastName" 
                                           class="form-input" 
                                           placeholder="Nhập tên"
                                           required>
                                    <div class="form-error" id="lastName-error"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="email">Email</label>
                                <input type="email" 
                                       id="email" 
                                       name="email" 
                                       class="form-input" 
                                       placeholder="Nhập địa chỉ email"
                                       required>
                                <div class="form-error" id="email-error"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="username">Tên đăng nhập</label>
                                <input type="text" 
                                       id="username" 
                                       name="username" 
                                       class="form-input" 
                                       placeholder="Nhập tên đăng nhập"
                                       required>
                                <div class="form-error" id="username-error"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="phone">Số điện thoại</label>
                                <input type="tel" 
                                       id="phone" 
                                       name="phone" 
                                       class="form-input" 
                                       placeholder="Nhập số điện thoại"
                                       required>
                                <div class="form-error" id="phone-error"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="password">Mật khẩu</label>
                                <div class="password-input-group">
                                    <input type="password" 
                                           id="password" 
                                           name="password" 
                                           class="form-input" 
                                           placeholder="Nhập mật khẩu"
                                           required>
                                    <button type="button" class="password-toggle" id="password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill"></div>
                                    </div>
                                    <span class="strength-text">Độ mạnh mật khẩu</span>
                                </div>
                                <div class="form-error" id="password-error"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="confirmPassword">Xác nhận mật khẩu</label>
                                <div class="password-input-group">
                                    <input type="password" 
                                           id="confirmPassword" 
                                           name="confirmPassword" 
                                           class="form-input" 
                                           placeholder="Nhập lại mật khẩu"
                                           required>
                                    <button type="button" class="password-toggle" id="confirm-password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-error" id="confirmPassword-error"></div>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="terms" name="terms" required>
                                    <span class="checkmark"></span>
                                    Tôi đồng ý với 
                                    <a href="#" class="auth-link" id="terms-link">Điều khoản sử dụng</a> 
                                    và 
                                    <a href="#" class="auth-link" id="privacy-link">Chính sách bảo mật</a>
                                </label>
                                <div class="form-error" id="terms-error"></div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg auth-submit" id="register-btn">
                                <span class="btn-text">Đăng ký</span>
                                <div class="btn-spinner" style="display: none;">
                                    <div class="spinner"></div>
                                </div>
                            </button>

                            <div class="auth-divider">
                                <span>Hoặc đăng ký với</span>
                            </div>

                            <div class="social-login">
                                <button type="button" class="btn btn-social btn-google">
                                    <i class="fab fa-google"></i>
                                    Google
                                </button>
                                <button type="button" class="btn btn-social btn-facebook">
                                    <i class="fab fa-facebook-f"></i>
                                    Facebook
                                </button>
                            </div>
                        </form>

                        <div class="auth-footer">
                            <p>Đã có tài khoản? 
                                <a href="#" data-route="/login" class="auth-link">Đăng nhập ngay</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupEventHandlers();
    },

    setupEventHandlers() {
        // Form submission
        const registerForm = utils.$('#register-form');
        registerForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // Password toggles
        this.setupPasswordToggles();

        // Password strength checker
        const passwordInput = utils.$('#password');
        passwordInput.addEventListener('input', () => {
            this.checkPasswordStrength(passwordInput.value);
        });

        // Confirm password validation
        const confirmPasswordInput = utils.$('#confirmPassword');
        confirmPasswordInput.addEventListener('input', () => {
            this.validatePasswordMatch();
        });

        // Real-time validation
        this.setupRealTimeValidation();

        // Terms and privacy links
        const termsLink = utils.$('#terms-link');
        const privacyLink = utils.$('#privacy-link');

        termsLink.addEventListener('click', (e) => {
            e.preventDefault();
            this.showTerms();
        });

        privacyLink.addEventListener('click', (e) => {
            e.preventDefault();
            this.showPrivacy();
        });

        // Social registration
        const googleBtn = utils.$('.btn-google');
        const facebookBtn = utils.$('.btn-facebook');

        googleBtn.addEventListener('click', () => {
            this.handleSocialRegister('google');
        });

        facebookBtn.addEventListener('click', () => {
            this.handleSocialRegister('facebook');
        });

        // Auto-focus first input
        const firstNameInput = utils.$('#firstName');
        if (firstNameInput) {
            firstNameInput.focus();
        }
    },

    setupPasswordToggles() {
        const toggles = [
            { toggle: '#password-toggle', input: '#password' },
            { toggle: '#confirm-password-toggle', input: '#confirmPassword' }
        ];

        toggles.forEach(({ toggle, input }) => {
            const toggleBtn = utils.$(toggle);
            const inputField = utils.$(input);

            toggleBtn.addEventListener('click', () => {
                const type = inputField.type === 'password' ? 'text' : 'password';
                inputField.type = type;
                
                const icon = toggleBtn.querySelector('i');
                icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
            });
        });
    },

    setupRealTimeValidation() {
        const fields = ['email', 'username', 'phone'];
        
        fields.forEach(field => {
            const input = utils.$(`#${field}`);
            input.addEventListener('blur', () => {
                this.validateField(field, input.value);
            });
        });
    },

    async handleRegister() {
        const form = utils.$('#register-form');
        const formData = new FormData(form);
        
        const userData = {
            firstName: formData.get('firstName').trim(),
            lastName: formData.get('lastName').trim(),
            email: formData.get('email').trim(),
            username: formData.get('username').trim(),
            phone: formData.get('phone').trim(),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword'),
            terms: formData.get('terms') === 'on'
        };

        // Validate form
        if (!this.validateForm(userData)) {
            return;
        }

        try {
            // Show loading
            this.setLoading(true);
            this.clearErrors();

            // Attempt registration
            const response = await auth.register(userData);

            // Success
            Toast.success('Đăng ký thành công! Chào mừng bạn đến với NaCinema!');
            
            // Redirect to home
            window.router.navigate('/');

        } catch (error) {
            console.error('Registration failed:', error);
            
            // Handle different error types
            if (error.status === 409) {
                if (error.message.includes('email')) {
                    this.showError('email', 'Email này đã được sử dụng');
                } else if (error.message.includes('username')) {
                    this.showError('username', 'Tên đăng nhập này đã được sử dụng');
                }
            } else {
                Toast.error(error.message || 'Đăng ký thất bại. Vui lòng thử lại.');
            }
        } finally {
            this.setLoading(false);
        }
    },

    validateForm(userData) {
        let isValid = true;
        
        // Clear previous errors
        this.clearErrors();

        // Validate first name
        if (!userData.firstName) {
            this.showError('firstName', 'Vui lòng nhập họ');
            isValid = false;
        }

        // Validate last name
        if (!userData.lastName) {
            this.showError('lastName', 'Vui lòng nhập tên');
            isValid = false;
        }

        // Validate email
        if (!userData.email) {
            this.showError('email', 'Vui lòng nhập email');
            isValid = false;
        } else if (!utils.validators.email(userData.email)) {
            this.showError('email', 'Email không hợp lệ');
            isValid = false;
        }

        // Validate username
        if (!userData.username) {
            this.showError('username', 'Vui lòng nhập tên đăng nhập');
            isValid = false;
        } else if (userData.username.length < 3) {
            this.showError('username', 'Tên đăng nhập phải có ít nhất 3 ký tự');
            isValid = false;
        }

        // Validate phone
        if (!userData.phone) {
            this.showError('phone', 'Vui lòng nhập số điện thoại');
            isValid = false;
        } else if (!utils.validators.phone(userData.phone)) {
            this.showError('phone', 'Số điện thoại không hợp lệ');
            isValid = false;
        }

        // Validate password
        if (!userData.password) {
            this.showError('password', 'Vui lòng nhập mật khẩu');
            isValid = false;
        } else if (userData.password.length < 6) {
            this.showError('password', 'Mật khẩu phải có ít nhất 6 ký tự');
            isValid = false;
        }

        // Validate confirm password
        if (userData.password !== userData.confirmPassword) {
            this.showError('confirmPassword', 'Mật khẩu xác nhận không khớp');
            isValid = false;
        }

        // Validate terms
        if (!userData.terms) {
            this.showError('terms', 'Vui lòng đồng ý với điều khoản sử dụng');
            isValid = false;
        }

        return isValid;
    },

    async validateField(field, value) {
        switch (field) {
            case 'email':
                if (value && utils.validators.email(value)) {
                    // Check if email exists
                    try {
                        await api.post('/auth/check-email', { email: value });
                    } catch (error) {
                        if (error.status === 409) {
                            this.showError('email', 'Email này đã được sử dụng');
                        }
                    }
                }
                break;
                
            case 'username':
                if (value && value.length >= 3) {
                    // Check if username exists
                    try {
                        await api.post('/auth/check-username', { username: value });
                    } catch (error) {
                        if (error.status === 409) {
                            this.showError('username', 'Tên đăng nhập này đã được sử dụng');
                        }
                    }
                }
                break;
        }
    },

    checkPasswordStrength(password) {
        const strengthBar = utils.$('.strength-fill');
        const strengthText = utils.$('.strength-text');
        
        let strength = 0;
        let text = 'Rất yếu';
        let color = '#ef4444';

        if (password.length >= 6) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        switch (strength) {
            case 0:
            case 1:
                text = 'Rất yếu';
                color = '#ef4444';
                break;
            case 2:
                text = 'Yếu';
                color = '#f59e0b';
                break;
            case 3:
                text = 'Trung bình';
                color = '#eab308';
                break;
            case 4:
                text = 'Mạnh';
                color = '#22c55e';
                break;
            case 5:
                text = 'Rất mạnh';
                color = '#16a34a';
                break;
        }

        strengthBar.style.width = `${(strength / 5) * 100}%`;
        strengthBar.style.backgroundColor = color;
        strengthText.textContent = text;
        strengthText.style.color = color;
    },

    validatePasswordMatch() {
        const password = utils.$('#password').value;
        const confirmPassword = utils.$('#confirmPassword').value;

        if (confirmPassword && password !== confirmPassword) {
            this.showError('confirmPassword', 'Mật khẩu xác nhận không khớp');
        } else {
            this.clearError('confirmPassword');
        }
    },

    showError(field, message) {
        const errorElement = utils.$(`#${field}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        const inputElement = utils.$(`#${field}`);
        if (inputElement) {
            inputElement.classList.add('error');
        }
    },

    clearError(field) {
        const errorElement = utils.$(`#${field}-error`);
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        }

        const inputElement = utils.$(`#${field}`);
        if (inputElement) {
            inputElement.classList.remove('error');
        }
    },

    clearErrors() {
        const errorElements = utils.$$('.form-error');
        errorElements.forEach(element => {
            element.textContent = '';
            element.style.display = 'none';
        });

        const inputElements = utils.$$('.form-input');
        inputElements.forEach(element => {
            element.classList.remove('error');
        });
    },

    setLoading(loading) {
        const registerBtn = utils.$('#register-btn');
        const btnText = registerBtn.querySelector('.btn-text');
        const btnSpinner = registerBtn.querySelector('.btn-spinner');

        if (loading) {
            registerBtn.disabled = true;
            btnText.style.display = 'none';
            btnSpinner.style.display = 'flex';
        } else {
            registerBtn.disabled = false;
            btnText.style.display = 'block';
            btnSpinner.style.display = 'none';
        }
    },

    showTerms() {
        Modal.show(`
            <div class="terms-content">
                <h3>Điều khoản sử dụng</h3>
                <p>Đây là nội dung điều khoản sử dụng của NaCinema...</p>
                <p>Nội dung chi tiết sẽ được cập nhật sau.</p>
            </div>
        `, {
            title: 'Điều khoản sử dụng',
            size: 'large'
        });
    },

    showPrivacy() {
        Modal.show(`
            <div class="privacy-content">
                <h3>Chính sách bảo mật</h3>
                <p>Đây là nội dung chính sách bảo mật của NaCinema...</p>
                <p>Nội dung chi tiết sẽ được cập nhật sau.</p>
            </div>
        `, {
            title: 'Chính sách bảo mật',
            size: 'large'
        });
    },

    async handleSocialRegister(provider) {
        try {
            Toast.info(`Đang chuyển hướng đến ${provider}...`);
            
            setTimeout(() => {
                Toast.warning('Tính năng đăng ký mạng xã hội đang được phát triển');
            }, 1000);
            
        } catch (error) {
            Toast.error(`Đăng ký ${provider} thất bại`);
        }
    }
};
