// ===== MOVIE REVIEWS COMPONENT =====

class MovieReviews {
  constructor(movieId, options = {}) {
    this.movieId = movieId;
    this.options = {
      showAddReview: true,
      maxReviews: 10,
      sortBy: "newest", // 'newest', 'oldest', 'rating'
      ...options,
    };

    this.reviews = [];
    this.isLoading = false;
    this.showReviewForm = false;
    this.selectedRating = 0;
    this.currentUser = auth.getUser();
  }

  async render() {
    await this.loadReviews();

    return `
            <div class="movie-reviews-container">
                <div class="reviews-header">
                    <h3>
                        <i class="fas fa-star"></i>
                        Đ<PERSON>h giá phim
                        ${
                          this.reviews.length > 0
                            ? `(${this.reviews.length})`
                            : ""
                        }
                    </h3>
                    
                    ${
                      this.options.showAddReview && auth.isAuthenticated()
                        ? `
                        <button class="btn btn-primary" id="add-review-btn">
                            <i class="fas fa-plus"></i>
                            Viết đ<PERSON> gi<PERSON>
                        </button>
                    `
                        : ""
                    }
                </div>

                ${this.renderReviewForm()}
                ${this.renderReviewsList()}
            </div>
        `;
  }

  renderReviewForm() {
    if (!this.showReviewForm) return "";

    return `
            <div class="review-form-container" id="review-form-container">
                <div class="review-form-header">
                    <h4>Viết đánh giá của bạn</h4>
                    <button class="close-form-btn" id="close-review-form">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form class="review-form" id="review-form">
                    <div class="rating-section">
                        <label class="form-label">Đánh giá của bạn *</label>
                        <div class="star-rating" id="star-rating">
                            ${Array.from(
                              { length: 5 },
                              (_, i) => `
                                <button type="button" class="star-btn ${
                                  i < this.selectedRating ? "active" : ""
                                }" 
                                        data-rating="${i + 1}">
                                    <i class="fas fa-star"></i>
                                </button>
                            `
                            ).join("")}
                        </div>
                        <div class="rating-text">
                            ${this.getRatingText(this.selectedRating)}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="review-content">
                            Nội dung đánh giá *
                        </label>
                        <textarea 
                            id="review-content" 
                            name="content"
                            class="form-input" 
                            rows="4" 
                            placeholder="Chia sẻ cảm nhận của bạn về bộ phim..."
                            required></textarea>
                        <div class="form-error" id="review-content-error"></div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-review">
                            Hủy
                        </button>
                        <button type="submit" class="btn btn-primary" id="submit-review">
                            <i class="fas fa-paper-plane"></i>
                            Gửi đánh giá
                        </button>
                    </div>
                </form>
            </div>
        `;
  }

  renderReviewsList() {
    if (this.isLoading) {
      return `
                <div class="reviews-loading">
                    <div class="spinner"></div>
                    <p>Đang tải đánh giá...</p>
                </div>
            `;
    }

    if (this.reviews.length === 0) {
      return `
                <div class="reviews-empty">
                    <div class="empty-icon">
                        <i class="fas fa-comment-slash"></i>
                    </div>
                    <h4>Chưa có đánh giá nào</h4>
                    <p>Hãy là người đầu tiên đánh giá bộ phim này!</p>
                    ${
                      this.options.showAddReview && auth.isAuthenticated()
                        ? `
                        <button class="btn btn-primary" onclick="movieReviews.showAddReviewForm()">
                            <i class="fas fa-star"></i>
                            Viết đánh giá đầu tiên
                        </button>
                    `
                        : ""
                    }
                </div>
            `;
    }

    return `
            <div class="reviews-list">
                <div class="reviews-controls">
                    <div class="reviews-stats">
                        <div class="average-rating">
                            <div class="rating-stars">
                                ${this.renderStars(this.getAverageRating())}
                            </div>
                            <span class="rating-value">${this.getAverageRating().toFixed(
                              1
                            )}/5</span>
                            <span class="rating-count">(${
                              this.reviews.length
                            } đánh giá)</span>
                        </div>
                    </div>
                    
                    <div class="reviews-sort">
                        <select class="form-select" id="reviews-sort">
                            <option value="newest" ${
                              this.options.sortBy === "newest" ? "selected" : ""
                            }>Mới nhất</option>
                            <option value="oldest" ${
                              this.options.sortBy === "oldest" ? "selected" : ""
                            }>Cũ nhất</option>
                            <option value="rating" ${
                              this.options.sortBy === "rating" ? "selected" : ""
                            }>Đánh giá cao</option>
                        </select>
                    </div>
                </div>
                
                <div class="reviews-items">
                    ${this.reviews
                      .map(review => this.renderReviewItem(review))
                      .join("")}
                </div>
                
                ${
                  this.reviews.length >= this.options.maxReviews
                    ? `
                    <div class="reviews-load-more">
                        <button class="btn btn-outline" id="load-more-reviews">
                            <i class="fas fa-chevron-down"></i>
                            Xem thêm đánh giá
                        </button>
                    </div>
                `
                    : ""
                }
            </div>
        `;
  }

  renderReviewItem(review) {
    const reviewDate = new Date(review.createdAt).toLocaleDateString("vi-VN");
    const userName =
      review.user?.fullName || review.user?.username || "Ẩn danh";
    const userInitial = userName.charAt(0).toUpperCase();

    return `
            <div class="review-item" data-review-id="${review.id}">
                <div class="review-header">
                    <div class="review-user">
                        <div class="user-avatar">
                            ${
                              review.user?.avatar
                                ? `
                                <img src="${review.user.avatar}" alt="${userName}">
                            `
                                : `
                                <span class="avatar-initial">${userInitial}</span>
                            `
                            }
                        </div>
                        <div class="user-info">
                            <div class="user-name">${userName}</div>
                            <div class="review-date">${reviewDate}</div>
                        </div>
                    </div>
                    
                    <div class="review-rating">
                        ${this.renderStars(review.rating)}
                    </div>
                </div>
                
                <div class="review-content">
                    <p>${review.content}</p>
                </div>
                
                <div class="review-actions">
                    <button class="review-action-btn" onclick="movieReviews.likeReview(${
                      review.id
                    })">
                        <i class="fas fa-thumbs-up"></i>
                        <span>Hữu ích</span>
                        ${
                          review.likes > 0
                            ? `<span class="action-count">(${review.likes})</span>`
                            : ""
                        }
                    </button>
                    
                    ${
                      this.currentUser && this.currentUser.id === review.userId
                        ? `
                        <button class="review-action-btn" onclick="movieReviews.editReview(${review.id})">
                            <i class="fas fa-edit"></i>
                            <span>Sửa</span>
                        </button>
                        <button class="review-action-btn delete" onclick="movieReviews.deleteReview(${review.id})">
                            <i class="fas fa-trash"></i>
                            <span>Xóa</span>
                        </button>
                    `
                        : ""
                    }
                </div>
            </div>
        `;
  }

  renderStars(rating) {
    return Array.from({ length: 5 }, (_, i) => {
      const filled = i < Math.floor(rating);
      const halfFilled = i === Math.floor(rating) && rating % 1 >= 0.5;

      return `
                <i class="fas fa-star ${
                  filled ? "filled" : halfFilled ? "half-filled" : ""
                }"></i>
            `;
    }).join("");
  }

  async loadReviews() {
    this.isLoading = true;

    try {
      this.reviews = await api.get(`/api/movies/${this.movieId}/reviews`);
      this.sortReviews();
    } catch (error) {
      console.error("Failed to load reviews:", error);
      this.reviews = [];
    } finally {
      this.isLoading = false;
    }
  }

  sortReviews() {
    switch (this.options.sortBy) {
      case "newest":
        this.reviews.sort(
          (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
        );
        break;
      case "oldest":
        this.reviews.sort(
          (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
        );
        break;
      case "rating":
        this.reviews.sort((a, b) => b.rating - a.rating);
        break;
    }
  }

  getAverageRating() {
    if (this.reviews.length === 0) return 0;
    const sum = this.reviews.reduce((acc, review) => acc + review.rating, 0);
    return sum / this.reviews.length;
  }

  getRatingText(rating) {
    const texts = {
      1: "Rất tệ",
      2: "Tệ",
      3: "Bình thường",
      4: "Tốt",
      5: "Xuất sắc",
    };
    return texts[rating] || "Chọn số sao";
  }

  setupEventHandlers() {
    // Add review button
    const addReviewBtn = utils.$("#add-review-btn");
    if (addReviewBtn) {
      addReviewBtn.addEventListener("click", () => this.showAddReviewForm());
    }

    // Close review form
    const closeFormBtn = utils.$("#close-review-form");
    if (closeFormBtn) {
      closeFormBtn.addEventListener("click", () => this.hideReviewForm());
    }

    // Cancel review
    const cancelBtn = utils.$("#cancel-review");
    if (cancelBtn) {
      cancelBtn.addEventListener("click", () => this.hideReviewForm());
    }

    // Star rating
    const starBtns = utils.$$(".star-btn");
    starBtns.forEach(btn => {
      btn.addEventListener("click", () => {
        this.selectedRating = parseInt(btn.dataset.rating);
        this.updateStarRating();
      });
    });

    // Review form submission
    const reviewForm = utils.$("#review-form");
    if (reviewForm) {
      reviewForm.addEventListener("submit", e => {
        e.preventDefault();
        this.submitReview();
      });
    }

    // Sort reviews
    const sortSelect = utils.$("#reviews-sort");
    if (sortSelect) {
      sortSelect.addEventListener("change", () => {
        this.options.sortBy = sortSelect.value;
        this.sortReviews();
        this.updateDisplay();
      });
    }

    // Load more reviews
    const loadMoreBtn = utils.$("#load-more-reviews");
    if (loadMoreBtn) {
      loadMoreBtn.addEventListener("click", () => this.loadMoreReviews());
    }
  }

  showAddReviewForm() {
    if (!auth.isAuthenticated()) {
      Toast.warning("Bạn cần đăng nhập để viết đánh giá");
      return;
    }

    this.showReviewForm = true;
    this.selectedRating = 0;
    this.updateDisplay();
  }

  hideReviewForm() {
    this.showReviewForm = false;
    this.selectedRating = 0;
    this.updateDisplay();
  }

  updateStarRating() {
    const starBtns = utils.$$(".star-btn");
    starBtns.forEach((btn, index) => {
      btn.classList.toggle("active", index < this.selectedRating);
    });

    const ratingText = utils.$(".rating-text");
    if (ratingText) {
      ratingText.textContent = this.getRatingText(this.selectedRating);
    }
  }

  async submitReview() {
    const contentInput = utils.$("#review-content");
    const content = contentInput.value.trim();

    // Validation
    if (this.selectedRating === 0) {
      Toast.error("Vui lòng chọn số sao đánh giá");
      return;
    }

    if (content.length < 10) {
      this.showFieldError(contentInput, "Đánh giá phải có ít nhất 10 ký tự");
      return;
    }

    try {
      const reviewData = {
        movieId: this.movieId,
        rating: this.selectedRating,
        content: content,
      };

      const newReview = await api.post(
        `/api/movies/${this.movieId}/reviews`,
        reviewData
      );

      // Add new review to the beginning of the list
      this.reviews.unshift(newReview);

      Toast.success("Đánh giá của bạn đã được gửi thành công!");
      this.hideReviewForm();
    } catch (error) {
      Toast.error(error.message || "Không thể gửi đánh giá. Vui lòng thử lại.");
    }
  }

  async likeReview(reviewId) {
    if (!auth.isAuthenticated()) {
      Toast.warning("Bạn cần đăng nhập để thích đánh giá");
      return;
    }

    try {
      await api.post(`/api/reviews/${reviewId}/like`);

      // Update review likes count
      const review = this.reviews.find(r => r.id === reviewId);
      if (review) {
        review.likes = (review.likes || 0) + 1;
        this.updateDisplay();
      }

      Toast.success("Đã thích đánh giá này");
    } catch (error) {
      Toast.error("Không thể thích đánh giá này");
    }
  }

  async editReview(reviewId) {
    const review = this.reviews.find(r => r.id === reviewId);
    if (!review) return;

    // Show edit form in modal
    const modalContent = `
            <div class="edit-review-modal">
                <form id="edit-review-form">
                    <div class="rating-section">
                        <label class="form-label">Đánh giá của bạn *</label>
                        <div class="star-rating" id="edit-star-rating">
                            ${Array.from(
                              { length: 5 },
                              (_, i) => `
                                <button type="button" class="star-btn ${
                                  i < review.rating ? "active" : ""
                                }"
                                        data-rating="${i + 1}">
                                    <i class="fas fa-star"></i>
                                </button>
                            `
                            ).join("")}
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="edit-review-content">
                            Nội dung đánh giá *
                        </label>
                        <textarea
                            id="edit-review-content"
                            name="content"
                            class="form-input"
                            rows="4"
                            required>${review.content}</textarea>
                    </div>
                </form>
            </div>
        `;

    const confirmed = await Modal.show(modalContent, {
      title: "Chỉnh sửa đánh giá",
      confirmText: "Cập nhật",
      cancelText: "Hủy",
    });

    if (confirmed) {
      await this.updateReview(reviewId);
    }
  }

  async updateReview(reviewId) {
    const form = utils.$("#edit-review-form");
    const content = form.querySelector("#edit-review-content").value.trim();
    const activeStars = form.querySelectorAll(".star-btn.active");
    const rating = activeStars.length;

    if (rating === 0 || content.length < 10) {
      Toast.error("Vui lòng kiểm tra lại thông tin đánh giá");
      return;
    }

    try {
      const updatedReview = await api.put(`/api/reviews/${reviewId}`, {
        rating,
        content,
      });

      // Update review in list
      const reviewIndex = this.reviews.findIndex(r => r.id === reviewId);
      if (reviewIndex !== -1) {
        this.reviews[reviewIndex] = {
          ...this.reviews[reviewIndex],
          ...updatedReview,
        };
        this.updateDisplay();
      }

      Toast.success("Đánh giá đã được cập nhật");
    } catch (error) {
      Toast.error("Không thể cập nhật đánh giá");
    }
  }

  async deleteReview(reviewId) {
    const confirmed = await Modal.confirm(
      "Bạn có chắc chắn muốn xóa đánh giá này?",
      { title: "Xác nhận xóa đánh giá" }
    );

    if (!confirmed) return;

    try {
      await api.delete(`/api/reviews/${reviewId}`);

      // Remove review from list
      this.reviews = this.reviews.filter(r => r.id !== reviewId);
      this.updateDisplay();

      Toast.success("Đã xóa đánh giá");
    } catch (error) {
      Toast.error("Không thể xóa đánh giá");
    }
  }

  async loadMoreReviews() {
    try {
      const offset = this.reviews.length;
      const moreReviews = await api.get(
        `/api/movies/${this.movieId}/reviews?offset=${offset}&limit=${this.options.maxReviews}`
      );

      this.reviews.push(...moreReviews);
      this.updateDisplay();
    } catch (error) {
      Toast.error("Không thể tải thêm đánh giá");
    }
  }

  showFieldError(field, message) {
    const errorElement = utils.$(`#${field.id}-error`);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = message ? "block" : "none";
    }

    field.classList.toggle("error", !!message);
  }

  updateDisplay() {
    // Re-render the component
    const container = utils.$(".movie-reviews-container");
    if (container) {
      this.render().then(html => {
        container.outerHTML = html;
        this.setupEventHandlers();
      });
    }
  }

  static async create(containerId, movieId, options = {}) {
    const container = utils.$(`#${containerId}`);
    if (!container) {
      console.error(`Container #${containerId} not found`);
      return null;
    }

    const movieReviews = new MovieReviews(movieId, options);
    const html = await movieReviews.render();
    container.innerHTML = html;
    movieReviews.setupEventHandlers();

    // Store instance globally for event handlers
    window.movieReviews = movieReviews;

    return movieReviews;
  }
}

// Export to global scope
window.MovieReviews = MovieReviews;
