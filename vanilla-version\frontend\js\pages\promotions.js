// ===== PROMOTIONS PAGE =====

window.pages.promotions = {
    promotions: [],
    currentCategory: 'all',

    async render() {
        const mainContent = utils.$('#main-content');
        
        try {
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="page-loading">
                        <div class="spinner"></div>
                        <p><PERSON><PERSON> tải danh sách khuyến mãi...</p>
                    </div>
                </div>
            `;

            // Load promotions
            await this.loadPromotions();
            
            // Render page
            this.renderPromotionsPage();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to render promotions page:', error);
            this.renderError('C<PERSON> lỗi xảy ra khi tải danh sách khuyến mãi.');
        }
    },

    async loadPromotions() {
        try {
            this.promotions = await api.get('/api/promotions');
        } catch (error) {
            console.error('Failed to load promotions:', error);
            // Mock data for demo
            this.promotions = [
                {
                    id: 1,
                    title: '<PERSON><PERSON><PERSON><PERSON> 50% vé xem phim cuối tuần',
                    description: '<PERSON><PERSON> dụng cho tất cả suất chiếu từ thứ 6 đến chủ nhật',
                    image: '/assets/images/promo1.jpg',
                    category: 'discount',
                    discount: 50,
                    validFrom: '2024-01-01',
                    validTo: '2024-12-31',
                    code: 'WEEKEND50',
                    status: 'active'
                },
                {
                    id: 2,
                    title: 'Combo bắp nước chỉ 99k',
                    description: 'Combo bắp rang bơ + 2 nước ngọt size L',
                    image: '/assets/images/promo2.jpg',
                    category: 'combo',
                    originalPrice: 150000,
                    salePrice: 99000,
                    validFrom: '2024-01-01',
                    validTo: '2024-12-31',
                    code: 'COMBO99',
                    status: 'active'
                }
            ];
        }
    },

    renderPromotionsPage() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="promotions-page">
                <!-- Hero Section -->
                <div class="hero-section">
                    <div class="hero-background">
                        <div class="hero-overlay"></div>
                    </div>
                    <div class="container">
                        <div class="hero-content">
                            <h1 class="hero-title">
                                <i class="fas fa-gift"></i>
                                Khuyến mãi đặc biệt
                            </h1>
                            <p class="hero-subtitle">
                                Khám phá những ưu đãi hấp dẫn và tiết kiệm chi phí xem phim
                            </p>
                            <div class="hero-stats">
                                <div class="stat-item">
                                    <span class="stat-number">${this.getActivePromotions().length}</span>
                                    <span class="stat-label">Ưu đãi đang có</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">50%</span>
                                    <span class="stat-label">Giảm tối đa</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Promotion -->
                ${this.renderFeaturedPromotion()}

                <!-- Categories Filter -->
                <div class="filters-section">
                    <div class="container">
                        <div class="filters-header">
                            <h2>Danh mục khuyến mãi</h2>
                        </div>
                        
                        <div class="category-filters">
                            <button class="filter-btn ${this.currentCategory === 'all' ? 'active' : ''}" 
                                    data-category="all">
                                <i class="fas fa-th"></i>
                                Tất cả
                            </button>
                            <button class="filter-btn ${this.currentCategory === 'discount' ? 'active' : ''}" 
                                    data-category="discount">
                                <i class="fas fa-percentage"></i>
                                Giảm giá vé
                            </button>
                            <button class="filter-btn ${this.currentCategory === 'combo' ? 'active' : ''}" 
                                    data-category="combo">
                                <i class="fas fa-utensils"></i>
                                Combo ăn uống
                            </button>
                            <button class="filter-btn ${this.currentCategory === 'membership' ? 'active' : ''}" 
                                    data-category="membership">
                                <i class="fas fa-crown"></i>
                                Thành viên VIP
                            </button>
                            <button class="filter-btn ${this.currentCategory === 'special' ? 'active' : ''}" 
                                    data-category="special">
                                <i class="fas fa-star"></i>
                                Ưu đãi đặc biệt
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Promotions Grid -->
                <div class="promotions-section">
                    <div class="container">
                        ${this.getFilteredPromotions().length > 0 ? `
                            <div class="promotions-grid">
                                ${this.getFilteredPromotions().map(promo => this.renderPromotionCard(promo)).join('')}
                            </div>
                        ` : `
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <h3>Không có khuyến mãi</h3>
                                <p>Hiện tại không có khuyến mãi nào trong danh mục này.</p>
                                <button class="btn btn-primary" onclick="window.pages.promotions.filterByCategory('all')">
                                    Xem tất cả khuyến mãi
                                </button>
                            </div>
                        `}
                    </div>
                </div>

                <!-- Newsletter Section -->
                <div class="newsletter-section">
                    <div class="container">
                        <div class="newsletter-content">
                            <div class="newsletter-text">
                                <h3>Đăng ký nhận thông báo khuyến mãi</h3>
                                <p>Nhận thông tin về các chương trình ưu đãi mới nhất</p>
                            </div>
                            <form class="newsletter-form" id="promo-newsletter-form">
                                <div class="newsletter-input-group">
                                    <input type="email" class="form-input" placeholder="Nhập email của bạn" required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-bell"></i>
                                        Đăng ký
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderFeaturedPromotion() {
        const featured = this.promotions.find(p => p.featured) || this.promotions[0];
        if (!featured) return '';

        return `
            <div class="featured-promotion">
                <div class="container">
                    <div class="featured-content">
                        <div class="featured-image">
                            <img src="${featured.image}" alt="${featured.title}">
                            <div class="featured-badge">
                                <i class="fas fa-star"></i>
                                Nổi bật
                            </div>
                        </div>
                        <div class="featured-info">
                            <h2 class="featured-title">${featured.title}</h2>
                            <p class="featured-description">${featured.description}</p>
                            
                            <div class="featured-details">
                                ${featured.discount ? `
                                    <div class="discount-info">
                                        <span class="discount-badge">-${featured.discount}%</span>
                                        <span class="discount-text">Giảm giá</span>
                                    </div>
                                ` : ''}
                                
                                ${featured.salePrice ? `
                                    <div class="price-info">
                                        <span class="sale-price">${utils.formatPrice(featured.salePrice)}</span>
                                        <span class="original-price">${utils.formatPrice(featured.originalPrice)}</span>
                                    </div>
                                ` : ''}
                            </div>
                            
                            <div class="featured-validity">
                                <i class="fas fa-calendar"></i>
                                Có hiệu lực từ ${utils.formatDate(featured.validFrom)} đến ${utils.formatDate(featured.validTo)}
                            </div>
                            
                            <div class="featured-actions">
                                <button class="btn btn-primary btn-lg" onclick="window.pages.promotions.usePromotion(${featured.id})">
                                    <i class="fas fa-ticket-alt"></i>
                                    Sử dụng ngay
                                </button>
                                <button class="btn btn-secondary" onclick="window.pages.promotions.copyPromoCode('${featured.code}')">
                                    <i class="fas fa-copy"></i>
                                    Sao chép mã: ${featured.code}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderPromotionCard(promo) {
        const isExpired = new Date(promo.validTo) < new Date();
        const daysLeft = Math.ceil((new Date(promo.validTo) - new Date()) / (1000 * 60 * 60 * 24));

        return `
            <div class="promotion-card ${isExpired ? 'expired' : ''}" data-promo-id="${promo.id}">
                <div class="promo-image">
                    <img src="${promo.image}" alt="${promo.title}">
                    ${promo.discount ? `
                        <div class="promo-discount">
                            -${promo.discount}%
                        </div>
                    ` : ''}
                    ${isExpired ? `
                        <div class="promo-expired">
                            Đã hết hạn
                        </div>
                    ` : daysLeft <= 7 ? `
                        <div class="promo-urgent">
                            Còn ${daysLeft} ngày
                        </div>
                    ` : ''}
                </div>
                
                <div class="promo-content">
                    <h3 class="promo-title">${promo.title}</h3>
                    <p class="promo-description">${promo.description}</p>
                    
                    ${promo.salePrice ? `
                        <div class="promo-price">
                            <span class="sale-price">${utils.formatPrice(promo.salePrice)}</span>
                            <span class="original-price">${utils.formatPrice(promo.originalPrice)}</span>
                        </div>
                    ` : ''}
                    
                    <div class="promo-validity">
                        <i class="fas fa-clock"></i>
                        Đến ${utils.formatDate(promo.validTo)}
                    </div>
                    
                    <div class="promo-code">
                        <span class="code-label">Mã:</span>
                        <span class="code-value">${promo.code}</span>
                        <button class="copy-code-btn" onclick="window.pages.promotions.copyPromoCode('${promo.code}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                
                <div class="promo-actions">
                    ${!isExpired ? `
                        <button class="btn btn-primary btn-block" onclick="window.pages.promotions.usePromotion(${promo.id})">
                            <i class="fas fa-ticket-alt"></i>
                            Sử dụng ngay
                        </button>
                    ` : `
                        <button class="btn btn-secondary btn-block" disabled>
                            <i class="fas fa-times"></i>
                            Đã hết hạn
                        </button>
                    `}
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Category filter buttons
        const filterBtns = utils.$$('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.target.closest('.filter-btn').dataset.category;
                this.filterByCategory(category);
            });
        });

        // Newsletter form
        const newsletterForm = utils.$('#promo-newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.subscribeNewsletter();
            });
        }

        // Promotion card hover effects
        const promoCards = utils.$$('.promotion-card');
        promoCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('hovered');
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('hovered');
            });
        });
    },

    filterByCategory(category) {
        this.currentCategory = category;
        this.renderPromotionsPage();
        this.setupEventHandlers();
    },

    getFilteredPromotions() {
        if (this.currentCategory === 'all') {
            return this.promotions.filter(p => p.status === 'active');
        }
        return this.promotions.filter(p => 
            p.category === this.currentCategory && p.status === 'active'
        );
    },

    getActivePromotions() {
        return this.promotions.filter(p => p.status === 'active');
    },

    async usePromotion(promoId) {
        const promo = this.promotions.find(p => p.id === promoId);
        if (!promo) return;

        // Store promotion code for use in booking
        utils.storage.set('selectedPromoCode', promo.code);
        
        Toast.success(`Đã chọn mã khuyến mãi: ${promo.code}`);
        
        // Navigate to home page to select movie
        window.router.navigate('/');
    },

    copyPromoCode(code) {
        navigator.clipboard.writeText(code).then(() => {
            Toast.success(`Đã sao chép mã: ${code}`);
        }).catch(() => {
            Toast.error('Không thể sao chép mã khuyến mãi');
        });
    },

    async subscribeNewsletter() {
        const form = utils.$('#promo-newsletter-form');
        const emailInput = form.querySelector('input[type="email"]');
        const email = emailInput.value.trim();

        if (!email || !utils.validators.email(email)) {
            Toast.error('Vui lòng nhập email hợp lệ');
            return;
        }

        try {
            await api.post('/api/newsletter/subscribe', { 
                email,
                type: 'promotions'
            });
            Toast.success('Đăng ký nhận thông báo khuyến mãi thành công!');
            emailInput.value = '';
            
        } catch (error) {
            if (error.message.includes('already subscribed')) {
                Toast.warning('Email này đã được đăng ký trước đó');
            } else {
                Toast.error('Không thể đăng ký. Vui lòng thử lại.');
            }
        }
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải trang</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.pages.promotions.render()">
                                Thử lại
                            </button>
                            <a href="#" class="btn btn-secondary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
