@echo off
echo ========================================
echo   NaCinema Frontend/Backend Separation
echo ========================================
echo.

:: Check if vanilla-version exists
if not exist "vanilla-version" (
    echo ERROR: vanilla-version directory not found!
    echo Please make sure you're in the project root directory.
    pause
    exit /b 1
)

echo Step 1: Creating frontend directory structure...
mkdir frontend 2>nul
mkdir frontend\css 2>nul
mkdir frontend\js 2>nul
mkdir frontend\js\components 2>nul
mkdir frontend\js\pages 2>nul
mkdir frontend\assets 2>nul
echo ✓ Frontend directories created

echo.
echo Step 2: Copying vanilla-version files to frontend...
copy "vanilla-version\index.html" "frontend\" >nul 2>&1
copy "vanilla-version\test-features.html" "frontend\" >nul 2>&1
copy "vanilla-version\final-test.html" "frontend\" >nul 2>&1

:: Copy CSS files
if exist "vanilla-version\css" (
    xcopy "vanilla-version\css\*" "frontend\css\" /Y /Q >nul 2>&1
)

:: Copy JS files
if exist "vanilla-version\js" (
    xcopy "vanilla-version\js\*" "frontend\js\" /E /Y /Q >nul 2>&1
)

echo ✓ Frontend files copied

echo.
echo Step 3: Creating backend directory structure...
mkdir backend 2>nul
mkdir backend\routes 2>nul
mkdir backend\models 2>nul
mkdir backend\middleware 2>nul
mkdir backend\config 2>nul
mkdir backend\utils 2>nul
echo ✓ Backend directories created

echo.
echo Step 4: Copying server files to backend...
if exist "server" (
    xcopy "server\*" "backend\" /E /Y /Q >nul 2>&1
)
if exist "shared" (
    xcopy "shared" "backend\shared\" /E /I /Y /Q >nul 2>&1
)
echo ✓ Backend files copied

echo.
echo Step 5: Creating package.json files...

:: Create frontend package.json
(
echo {
echo   "name": "nacinema-frontend",
echo   "version": "1.0.0",
echo   "description": "NaCinema Frontend - Vanilla JavaScript",
echo   "main": "index.html",
echo   "scripts": {
echo     "dev": "npx http-server . -p 3000 -c-1 --cors",
echo     "start": "npx http-server . -p 3000 --cors",
echo     "build": "echo 'No build step required'",
echo     "preview": "npx http-server . -p 4173 --cors"
echo   },
echo   "keywords": ["cinema", "movie", "booking", "vanilla-js"],
echo   "author": "NaCinema Team",
echo   "license": "MIT",
echo   "devDependencies": {
echo     "http-server": "^14.1.1"
echo   }
echo }
) > frontend\package.json

:: Create backend package.json
(
echo {
echo   "name": "nacinema-backend",
echo   "version": "1.0.0",
echo   "description": "NaCinema Backend - Node.js/Express API",
echo   "main": "server.js",
echo   "scripts": {
echo     "dev": "tsx watch server.js",
echo     "start": "node server.js",
echo     "build": "tsc"
echo   },
echo   "dependencies": {
echo     "express": "^4.18.2",
echo     "cors": "^2.8.5",
echo     "dotenv": "^16.3.1",
echo     "bcrypt": "^5.1.1",
echo     "jsonwebtoken": "^9.0.2",
echo     "mongodb": "^6.3.0"
echo   },
echo   "devDependencies": {
echo     "tsx": "^4.7.0",
echo     "typescript": "^5.3.3"
echo   }
echo }
) > backend\package.json

echo ✓ Package.json files created

echo.
echo Step 6: Creating main server file...
(
echo const express = require('express'^);
echo const cors = require('cors'^);
echo require('dotenv'^).config(^);
echo.
echo const app = express(^);
echo const PORT = process.env.PORT ^|^| 5000;
echo.
echo // Middleware
echo app.use(cors({
echo   origin: ['http://localhost:3000', 'http://localhost:4173'],
echo   credentials: true
echo }^)^);
echo.
echo app.use(express.json(^)^);
echo app.use(express.urlencoded({ extended: true }^)^);
echo.
echo // Health check
echo app.get('/health', (req, res^) =^> {
echo   res.json({ status: 'OK', timestamp: new Date(^).toISOString(^) }^);
echo }^);
echo.
echo // Basic API routes
echo app.get('/api/movies', (req, res^) =^> {
echo   res.json([{
echo     id: 1,
echo     title: 'Sample Movie',
echo     genre: 'Action',
echo     duration: 120,
echo     poster: '/assets/images/sample.jpg',
echo     description: 'Sample movie for testing'
echo   }]^);
echo }^);
echo.
echo app.listen(PORT, (^) =^> {
echo   console.log(`Backend running on http://localhost:${PORT}`^);
echo }^);
) > backend\server.js

echo ✓ Main server file created

echo.
echo Step 7: Creating development scripts...
(
echo @echo off
echo echo Starting NaCinema Development Servers...
echo echo.
echo echo Starting Backend Server...
echo start "Backend" cmd /k "cd backend && npm run dev"
echo timeout /t 3 /nobreak ^>nul
echo echo Starting Frontend Server...
echo start "Frontend" cmd /k "cd frontend && npm run dev"
echo echo.
echo echo ✓ Both servers started!
echo echo Backend: http://localhost:5000
echo echo Frontend: http://localhost:3000
) > dev-start.bat

echo ✓ Development scripts created

echo.
echo ========================================
echo   🎉 SEPARATION COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo 📁 Project structure:
echo   ├── frontend/     (Vanilla JavaScript)
echo   ├── backend/      (Node.js/Express)
echo   └── dev-start.bat (Development script)
echo.
echo 🚀 Next steps:
echo   1. Install frontend dependencies:
echo      cd frontend && npm install
echo.
echo   2. Install backend dependencies:
echo      cd backend && npm install
echo.
echo   3. Start both servers:
echo      dev-start.bat
echo.
echo 🌐 URLs:
echo   Frontend: http://localhost:3000
echo   Backend:  http://localhost:5000
echo   Health:   http://localhost:5000/health
echo.
pause
