// ===== NOT FOUND PAGE =====

window.pages.notFound = {
    render() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="not-found-page">
                <div class="container">
                    <div class="not-found-content">
                        <!-- 404 Animation -->
                        <div class="not-found-animation">
                            <div class="film-reel">
                                <div class="reel reel-1">
                                    <div class="reel-center"></div>
                                    <div class="reel-holes">
                                        ${Array.from({length: 8}, (_, i) => `
                                            <div class="hole" style="transform: rotate(${i * 45}deg)"></div>
                                        `).join('')}
                                    </div>
                                </div>
                                <div class="reel reel-2">
                                    <div class="reel-center"></div>
                                    <div class="reel-holes">
                                        ${Array.from({length: 8}, (_, i) => `
                                            <div class="hole" style="transform: rotate(${i * 45}deg)"></div>
                                        `).join('')}
                                    </div>
                                </div>
                                <div class="film-strip"></div>
                            </div>
                            
                            <div class="error-code">
                                <span class="digit">4</span>
                                <span class="digit">0</span>
                                <span class="digit">4</span>
                            </div>
                        </div>

                        <!-- Error Message -->
                        <div class="not-found-message">
                            <h1 class="error-title">Trang không tìm thấy</h1>
                            <p class="error-subtitle">
                                Rất tiếc, trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.
                            </p>
                            <p class="error-description">
                                Có thể bạn đã nhập sai địa chỉ hoặc trang này đã không còn khả dụng.
                            </p>
                        </div>

                        <!-- Suggestions -->
                        <div class="not-found-suggestions">
                            <h3>Bạn có thể thử:</h3>
                            <div class="suggestions-grid">
                                <div class="suggestion-card" onclick="window.router.navigate('/')">
                                    <div class="suggestion-icon">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <h4>Về trang chủ</h4>
                                    <p>Khám phá các bộ phim đang chiếu</p>
                                </div>
                                
                                <div class="suggestion-card" onclick="window.pages.notFound.searchMovies()">
                                    <div class="suggestion-icon">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <h4>Tìm kiếm phim</h4>
                                    <p>Tìm phim yêu thích của bạn</p>
                                </div>
                                
                                <div class="suggestion-card" onclick="window.router.navigate('/coming-soon')">
                                    <div class="suggestion-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <h4>Phim sắp chiếu</h4>
                                    <p>Xem những bộ phim sắp ra mắt</p>
                                </div>
                                
                                <div class="suggestion-card" onclick="window.router.navigate('/promotions')">
                                    <div class="suggestion-icon">
                                        <i class="fas fa-gift"></i>
                                    </div>
                                    <h4>Khuyến mãi</h4>
                                    <p>Khám phá các ưu đãi hấp dẫn</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Search -->
                        <div class="quick-search">
                            <h3>Hoặc tìm kiếm nhanh:</h3>
                            <form class="search-form" id="quick-search-form">
                                <div class="search-input-group">
                                    <input type="text" class="form-input" placeholder="Tìm kiếm phim, rạp chiếu..." id="quick-search-input">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                        Tìm kiếm
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Popular Movies -->
                        <div class="popular-movies" id="popular-movies-section">
                            <!-- Will be populated by loadPopularMovies() -->
                        </div>

                        <!-- Action Buttons -->
                        <div class="not-found-actions">
                            <button class="btn btn-primary btn-lg" onclick="window.router.navigate('/')">
                                <i class="fas fa-home"></i>
                                Về trang chủ
                            </button>
                            <button class="btn btn-secondary" onclick="window.history.back()">
                                <i class="fas fa-arrow-left"></i>
                                Quay lại
                            </button>
                            <button class="btn btn-outline" onclick="window.pages.notFound.reportIssue()">
                                <i class="fas fa-bug"></i>
                                Báo lỗi
                            </button>
                        </div>

                        <!-- Help Section -->
                        <div class="help-section">
                            <h3>Cần hỗ trợ?</h3>
                            <div class="help-options">
                                <a href="mailto:<EMAIL>" class="help-link">
                                    <i class="fas fa-envelope"></i>
                                    Email hỗ trợ
                                </a>
                                <a href="tel:1900-xxxx" class="help-link">
                                    <i class="fas fa-phone"></i>
                                    Hotline: 1900-xxxx
                                </a>
                                <button class="help-link" onclick="window.pages.notFound.openLiveChat()">
                                    <i class="fas fa-comments"></i>
                                    Chat trực tuyến
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupEventHandlers();
        this.loadPopularMovies();
    },

    setupEventHandlers() {
        // Quick search form
        const searchForm = utils.$('#quick-search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performQuickSearch();
            });
        }

        // Add some interactive animations
        this.setupAnimations();
    },

    setupAnimations() {
        // Animate film reels
        const reels = utils.$$('.reel');
        reels.forEach((reel, index) => {
            reel.style.animationDelay = `${index * 0.5}s`;
            reel.classList.add('spinning');
        });

        // Animate error digits
        const digits = utils.$$('.digit');
        digits.forEach((digit, index) => {
            setTimeout(() => {
                digit.classList.add('bounce');
            }, index * 200);
        });

        // Add hover effects to suggestion cards
        const suggestionCards = utils.$$('.suggestion-card');
        suggestionCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    },

    async loadPopularMovies() {
        try {
            const movies = await movieAPI.getMovies();
            const popularMovies = movies.slice(0, 4); // Get first 4 movies

            const popularSection = utils.$('#popular-movies-section');
            if (popularMovies.length > 0) {
                popularSection.innerHTML = `
                    <h3>Phim phổ biến</h3>
                    <div class="popular-movies-grid">
                        ${popularMovies.map(movie => `
                            <div class="popular-movie-card" onclick="window.router.navigate('/movies/${movie.id}')">
                                <img src="${movie.poster}" alt="${movie.title}" class="popular-movie-poster">
                                <div class="popular-movie-info">
                                    <h4>${movie.title}</h4>
                                    <p>${movie.genre}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Failed to load popular movies:', error);
        }
    },

    performQuickSearch() {
        const searchInput = utils.$('#quick-search-input');
        const query = searchInput.value.trim();
        
        if (!query) {
            Toast.warning('Vui lòng nhập từ khóa tìm kiếm');
            return;
        }

        // Navigate to home page with search query
        window.router.navigate(`/?search=${encodeURIComponent(query)}`);
    },

    searchMovies() {
        // Show search modal or navigate to search page
        const searchQuery = prompt('Nhập tên phim bạn muốn tìm:');
        if (searchQuery) {
            window.router.navigate(`/?search=${encodeURIComponent(searchQuery)}`);
        }
    },

    reportIssue() {
        const issueModal = `
            <div class="issue-report-modal">
                <h3>Báo cáo vấn đề</h3>
                <form id="issue-report-form">
                    <div class="form-group">
                        <label class="form-label">Mô tả vấn đề:</label>
                        <textarea class="form-input" rows="4" placeholder="Vui lòng mô tả chi tiết vấn đề bạn gặp phải..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email liên hệ (tùy chọn):</label>
                        <input type="email" class="form-input" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">URL trang lỗi:</label>
                        <input type="text" class="form-input" value="${window.location.href}" readonly>
                    </div>
                </form>
            </div>
        `;

        Modal.show(issueModal, {
            title: 'Báo cáo lỗi',
            confirmText: 'Gửi báo cáo',
            cancelText: 'Hủy'
        }).then(confirmed => {
            if (confirmed) {
                this.submitIssueReport();
            }
        });
    },

    async submitIssueReport() {
        const form = utils.$('#issue-report-form');
        const formData = new FormData(form);
        
        try {
            await api.post('/api/support/report-issue', {
                description: formData.get('description'),
                email: formData.get('email'),
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
            
            Toast.success('Cảm ơn bạn đã báo cáo. Chúng tôi sẽ xem xét và khắc phục sớm nhất.');
        } catch (error) {
            Toast.error('Không thể gửi báo cáo. Vui lòng thử lại hoặc liên hệ trực tiếp.');
        }
    },

    openLiveChat() {
        // In a real application, this would open a live chat widget
        Toast.info('Tính năng chat trực tuyến sẽ được cập nhật sớm. Vui lòng liên hệ qua email hoặc hotline.');
    }
};
