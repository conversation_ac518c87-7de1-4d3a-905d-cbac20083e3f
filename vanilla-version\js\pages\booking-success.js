// ===== BOOKING SUCCESS PAGE =====

window.pages.bookingSuccess = {
    bookingData: null,

    async render(bookingCode) {
        const mainContent = utils.$('#main-content');
        
        try {
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="booking-success-loading">
                        <div class="spinner"></div>
                        <p>Đang tải thông tin đặt vé...</p>
                    </div>
                </div>
            `;

            // Load booking data
            await this.loadBookingData(bookingCode);
            
            // Render success page
            this.renderSuccessPage();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to load booking data:', error);
            this.renderError('Không tìm thấy thông tin đặt vé.');
        }
    },

    async loadBookingData(bookingCode) {
        try {
            this.bookingData = await bookingAPI.getBookingByCode(bookingCode);
        } catch (error) {
            console.error('Failed to load booking:', error);
            throw error;
        }
    },

    renderSuccessPage() {
        const mainContent = utils.$('#main-content');
        const booking = this.bookingData;

        mainContent.innerHTML = `
            <div class="booking-success-page">
                <div class="container">
                    <div class="success-content">
                        <!-- Success Header -->
                        <div class="success-header">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h1 class="success-title">Đặt vé thành công!</h1>
                            <p class="success-subtitle">
                                Cảm ơn bạn đã sử dụng dịch vụ của NaCinema. 
                                Thông tin vé đã được gửi đến email của bạn.
                            </p>
                        </div>

                        <!-- Booking Details -->
                        <div class="booking-details-card">
                            <div class="card-header">
                                <h2>Thông tin vé</h2>
                                <div class="booking-code">
                                    <span>Mã đặt vé:</span>
                                    <strong id="booking-code">${booking.code}</strong>
                                    <button class="copy-btn" id="copy-code" title="Sao chép mã">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="booking-info">
                                <div class="movie-section">
                                    <div class="movie-poster">
                                        <img src="${booking.movie.poster}" alt="${booking.movie.title}">
                                    </div>
                                    <div class="movie-details">
                                        <h3 class="movie-title">${booking.movie.title}</h3>
                                        <div class="movie-meta">
                                            <p><i class="fas fa-tag"></i> ${booking.movie.genre}</p>
                                            <p><i class="fas fa-clock"></i> ${booking.movie.duration} phút</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="showtime-section">
                                    <h4>Thông tin suất chiếu</h4>
                                    <div class="showtime-details">
                                        <div class="detail-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>${utils.formatDate(booking.showtime.date)}</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-clock"></i>
                                            <span>${utils.formatTime(booking.showtime.startTime)}</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>${booking.cinema.name}</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-door-open"></i>
                                            <span>Phòng ${booking.room.name}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="seats-section">
                                    <h4>Ghế đã đặt</h4>
                                    <div class="booked-seats">
                                        ${booking.seats.map(seat => `
                                            <span class="seat-badge">${seat.seatNumber}</span>
                                        `).join('')}
                                    </div>
                                </div>

                                <div class="customer-section">
                                    <h4>Thông tin khách hàng</h4>
                                    <div class="customer-details">
                                        <div class="detail-item">
                                            <i class="fas fa-user"></i>
                                            <span>${booking.customer.fullName}</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-phone"></i>
                                            <span>${booking.customer.phone}</span>
                                        </div>
                                        ${booking.customer.email ? `
                                            <div class="detail-item">
                                                <i class="fas fa-envelope"></i>
                                                <span>${booking.customer.email}</span>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>

                                <div class="payment-section">
                                    <h4>Thông tin thanh toán</h4>
                                    <div class="payment-details">
                                        <div class="payment-item">
                                            <span>Phương thức:</span>
                                            <span>${this.getPaymentMethodName(booking.paymentMethod)}</span>
                                        </div>
                                        <div class="payment-item">
                                            <span>Trạng thái:</span>
                                            <span class="status-badge status-${booking.paymentStatus}">
                                                ${this.getPaymentStatusText(booking.paymentStatus)}
                                            </span>
                                        </div>
                                        <div class="payment-item total">
                                            <span>Tổng tiền:</span>
                                            <span class="amount">${utils.formatPrice(booking.totalAmount)}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- QR Code -->
                        <div class="qr-section">
                            <h3>Mã QR vé</h3>
                            <div class="qr-container">
                                <div class="qr-code" id="qr-code">
                                    <!-- QR code will be generated here -->
                                </div>
                                <p class="qr-note">
                                    Vui lòng xuất trình mã QR này tại rạp để vào xem phim
                                </p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-primary" id="download-ticket">
                                <i class="fas fa-download"></i>
                                Tải vé PDF
                            </button>
                            <button class="btn btn-secondary" id="send-email">
                                <i class="fas fa-envelope"></i>
                                Gửi email
                            </button>
                            <button class="btn btn-secondary" id="add-calendar">
                                <i class="fas fa-calendar-plus"></i>
                                Thêm vào lịch
                            </button>
                        </div>

                        <!-- Important Notes -->
                        <div class="important-notes">
                            <h4><i class="fas fa-info-circle"></i> Lưu ý quan trọng</h4>
                            <ul>
                                <li>Vui lòng có mặt tại rạp trước giờ chiếu ít nhất 15 phút</li>
                                <li>Mang theo giấy tờ tùy thân để đối chiếu thông tin</li>
                                <li>Vé đã mua không thể đổi trả hoặc hoàn tiền</li>
                                <li>Liên hệ hotline 1900-xxxx nếu cần hỗ trợ</li>
                            </ul>
                        </div>

                        <!-- Navigation -->
                        <div class="navigation-buttons">
                            <a href="#" class="btn btn-outline" data-route="/">
                                <i class="fas fa-home"></i>
                                Về trang chủ
                            </a>
                            <a href="#" class="btn btn-outline" data-route="/bookings">
                                <i class="fas fa-ticket-alt"></i>
                                Vé của tôi
                            </a>
                            <button class="btn btn-primary" id="book-more">
                                <i class="fas fa-plus"></i>
                                Đặt vé khác
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Copy booking code
        const copyBtn = utils.$('#copy-code');
        copyBtn.addEventListener('click', () => {
            const bookingCode = utils.$('#booking-code').textContent;
            navigator.clipboard.writeText(bookingCode).then(() => {
                Toast.success('Đã sao chép mã đặt vé!');
            });
        });

        // Download ticket
        const downloadBtn = utils.$('#download-ticket');
        downloadBtn.addEventListener('click', () => {
            this.downloadTicket();
        });

        // Send email
        const emailBtn = utils.$('#send-email');
        emailBtn.addEventListener('click', () => {
            this.sendEmail();
        });

        // Add to calendar
        const calendarBtn = utils.$('#add-calendar');
        calendarBtn.addEventListener('click', () => {
            this.addToCalendar();
        });

        // Book more tickets
        const bookMoreBtn = utils.$('#book-more');
        bookMoreBtn.addEventListener('click', () => {
            window.router.navigate('/');
        });

        // Generate QR code
        this.generateQRCode();

        // Auto-scroll to top
        window.scrollTo(0, 0);
    },

    generateQRCode() {
        const qrContainer = utils.$('#qr-code');
        const qrData = JSON.stringify({
            code: this.bookingData.code,
            movie: this.bookingData.movie.title,
            showtime: this.bookingData.showtime.startTime,
            seats: this.bookingData.seats.map(s => s.seatNumber)
        });

        // For demo purposes, show a placeholder
        qrContainer.innerHTML = `
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
                <p>QR Code</p>
                <small>${this.bookingData.code}</small>
            </div>
        `;
    },

    async downloadTicket() {
        try {
            Toast.info('Đang tạo file PDF...');
            
            // In a real app, this would call an API to generate PDF
            setTimeout(() => {
                Toast.success('Tải vé thành công!');
            }, 2000);
            
        } catch (error) {
            Toast.error('Không thể tải vé. Vui lòng thử lại.');
        }
    },

    async sendEmail() {
        try {
            const email = await Modal.prompt(
                'Nhập địa chỉ email để nhận vé:',
                {
                    title: 'Gửi vé qua email',
                    defaultValue: this.bookingData.customer.email || '',
                    placeholder: '<EMAIL>'
                }
            );

            if (email && utils.validators.email(email)) {
                Toast.info('Đang gửi email...');
                
                // Call API to send email
                await api.post('/bookings/send-email', {
                    bookingCode: this.bookingData.code,
                    email: email
                });
                
                Toast.success('Đã gửi vé đến email thành công!');
            } else if (email) {
                Toast.error('Email không hợp lệ');
            }
        } catch (error) {
            Toast.error('Không thể gửi email. Vui lòng thử lại.');
        }
    },

    addToCalendar() {
        const booking = this.bookingData;
        const startDate = new Date(`${booking.showtime.date} ${booking.showtime.startTime}`);
        const endDate = new Date(startDate.getTime() + (booking.movie.duration * 60000));

        const calendarEvent = {
            title: `Xem phim: ${booking.movie.title}`,
            start: startDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
            end: endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
            description: `Mã vé: ${booking.code}\\nRạp: ${booking.cinema.name}\\nPhòng: ${booking.room.name}\\nGhế: ${booking.seats.map(s => s.seatNumber).join(', ')}`,
            location: booking.cinema.address || booking.cinema.name
        };

        const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(calendarEvent.title)}&dates=${calendarEvent.start}/${calendarEvent.end}&details=${encodeURIComponent(calendarEvent.description)}&location=${encodeURIComponent(calendarEvent.location)}`;

        window.open(calendarUrl, '_blank');
    },

    getPaymentMethodName(method) {
        const methods = {
            'vnpay': 'VNPay',
            'momo': 'MoMo',
            'zalopay': 'ZaloPay',
            'banking': 'Internet Banking'
        };
        return methods[method] || method;
    },

    getPaymentStatusText(status) {
        const statuses = {
            'pending': 'Đang xử lý',
            'completed': 'Thành công',
            'failed': 'Thất bại',
            'cancelled': 'Đã hủy'
        };
        return statuses[status] || status;
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Không tìm thấy thông tin đặt vé</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <a href="#" class="btn btn-primary" data-route="/">
                                Về trang chủ
                            </a>
                            <a href="#" class="btn btn-secondary" data-route="/bookings">
                                Vé của tôi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
