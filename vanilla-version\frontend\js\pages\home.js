// ===== HOME PAGE =====

window.pages.home = {
    movies: [],
    filteredMovies: [],
    currentGenre: 'all',
    currentSort: 'popularity',
    searchQuery: '',

    async render() {
        const mainContent = utils.$('#main-content');
        
        try {
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="page-loading">
                        <div class="spinner"></div>
                        <p>Đang tải danh sách phim...</p>
                    </div>
                </div>
            `;

            // Load movies
            await this.loadMovies();
            
            // Check for URL parameters
            this.handleURLParams();
            
            // Render page
            this.renderHomePage();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to render home page:', error);
            this.renderError('Có lỗi xảy ra khi tải danh sách phim.');
        }
    },

    async loadMovies() {
        try {
            // Try to load from API first, fallback to mock data
            try {
                this.movies = await movieAPI.getMovies();
            } catch (apiError) {
                console.warn('API not available, using mock data');
                this.movies = this.getMockMovies();
            }
            
            this.filteredMovies = [...this.movies];
            
        } catch (error) {
            console.error('Failed to load movies:', error);
            this.movies = this.getMockMovies();
            this.filteredMovies = [...this.movies];
        }
    },

    getMockMovies() {
        return [
            {
                id: 1,
                title: 'Avatar: The Way of Water',
                genre: 'Khoa học viễn tưởng',
                duration: 192,
                rating: 8.5,
                poster: 'https://image.tmdb.org/t/p/w500/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg',
                description: 'Jake Sully sống cùng gia đình mới của mình trên hành tinh Pandora. Khi một mối đe dọa quen thuộc trở lại để hoàn thành những gì đã bắt đầu trước đây, Jake phải làm việc với Neytiri và quân đội của chủng tộc Na\'vi để bảo vệ hành tinh của họ.',
                status: 'active',
                releaseDate: '2022-12-16'
            },
            {
                id: 2,
                title: 'Top Gun: Maverick',
                genre: 'Hành động',
                duration: 130,
                rating: 9.0,
                poster: 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                description: 'Sau hơn ba mười năm phục vụ như một trong những phi công hàng đầu của Hải quân, Pete "Maverick" Mitchell đang ở nơi anh thuộc về, thúc đẩy phong bì như một phi công thử nghiệm dũng cảm.',
                status: 'active',
                releaseDate: '2022-05-27'
            },
            {
                id: 3,
                title: 'Black Panther: Wakanda Forever',
                genre: 'Hành động',
                duration: 161,
                rating: 7.8,
                poster: 'https://image.tmdb.org/t/p/w500/sv1xJUazXeYqALzczSZ3O6nkH75.jpg',
                description: 'Nữ hoàng Ramonda, Shuri, M\'Baku, Okoye và Dora Milaje chiến đấu để bảo vệ quốc gia của họ khỏi các thế lực can thiệp sau cái chết của Vua T\'Challa.',
                status: 'active',
                releaseDate: '2022-11-11'
            }
        ];
    },

    handleURLParams() {
        const urlParams = new URLSearchParams(window.location.search);
        
        // Handle search query
        const search = urlParams.get('search');
        if (search) {
            this.searchQuery = search;
            this.filterMovies();
        }
        
        // Handle genre filter
        const genre = urlParams.get('genre');
        if (genre) {
            this.currentGenre = genre;
            this.filterMovies();
        }
        
        // Handle cinema filter
        const cinema = urlParams.get('cinema');
        if (cinema) {
            // Filter movies by cinema (if API supports it)
            this.filterMovies();
        }
    },

    renderHomePage() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="home-page">
                <!-- Hero Section -->
                <div class="hero-section">
                    <div class="hero-background">
                        <div class="hero-overlay"></div>
                    </div>
                    <div class="container">
                        <div class="hero-content">
                            <h1 class="hero-title">Khám phá thế giới điện ảnh</h1>
                            <p class="hero-subtitle">
                                Trải nghiệm những bộ phim blockbuster mới nhất với chất lượng hình ảnh và âm thanh tuyệt vời
                            </p>
                            <div class="hero-actions">
                                <button class="btn btn-primary btn-lg" onclick="window.pages.home.scrollToMovies()">
                                    <i class="fas fa-play"></i>
                                    Xem phim ngay
                                </button>
                                <button class="btn btn-secondary btn-lg" onclick="window.router.navigate('/coming-soon')">
                                    <i class="fas fa-calendar-alt"></i>
                                    Phim sắp chiếu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="filters-section" id="movies-section">
                    <div class="container">
                        <div class="filters-header">
                            <h2>Phim đang chiếu</h2>
                            <div class="filters-controls">
                                <div class="search-filter">
                                    <input type="text" class="form-input" placeholder="Tìm kiếm phim..." 
                                           id="movie-search" value="${this.searchQuery}">
                                    <i class="fas fa-search"></i>
                                </div>
                                <select class="form-select" id="sort-select">
                                    <option value="popularity">Phổ biến nhất</option>
                                    <option value="rating">Đánh giá cao</option>
                                    <option value="newest">Mới nhất</option>
                                    <option value="title">Tên A-Z</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="genre-filters">
                            <button class="filter-btn ${this.currentGenre === 'all' ? 'active' : ''}" 
                                    data-genre="all">
                                Tất cả
                            </button>
                            ${this.getUniqueGenres().map(genre => `
                                <button class="filter-btn ${this.currentGenre === genre ? 'active' : ''}" 
                                        data-genre="${genre}">
                                    ${genre}
                                </button>
                            `).join('')}
                        </div>
                        
                        <div class="results-info">
                            <span class="results-count">
                                Hiển thị ${this.filteredMovies.length} phim
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Movies Grid -->
                <div class="movies-section">
                    <div class="container">
                        ${this.filteredMovies.length > 0 ? `
                            <div class="movies-grid">
                                ${this.filteredMovies.map(movie => this.renderMovieCard(movie)).join('')}
                            </div>
                        ` : `
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-film"></i>
                                </div>
                                <h3>Không tìm thấy phim</h3>
                                <p>Không có phim nào phù hợp với tiêu chí tìm kiếm của bạn.</p>
                                <button class="btn btn-primary" onclick="window.pages.home.clearFilters()">
                                    Xem tất cả phim
                                </button>
                            </div>
                        `}
                    </div>
                </div>

                <!-- Features Section -->
                <div class="features-section">
                    <div class="container">
                        <h2 class="section-title">Tại sao chọn NaCinema?</h2>
                        <div class="features-grid">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <h3>Đặt vé dễ dàng</h3>
                                <p>Đặt vé online nhanh chóng, chọn ghế theo ý muốn</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <h3>Chất lượng cao</h3>
                                <p>Hình ảnh 4K, âm thanh Dolby Atmos</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <h3>Ưu đãi hấp dẫn</h3>
                                <p>Nhiều chương trình khuyến mãi và giảm giá</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <h3>Tiện lợi mọi lúc</h3>
                                <p>Truy cập dễ dàng trên mọi thiết bị</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderMovieCard(movie) {
        return `
            <div class="movie-card" data-movie-id="${movie.id}">
                <div class="movie-poster-container">
                    <img src="${movie.poster}" alt="${movie.title}" class="movie-poster">
                    <div class="movie-overlay">
                        <div class="movie-actions">
                            <button class="btn btn-primary" onclick="window.pages.home.viewMovieDetail(${movie.id})">
                                <i class="fas fa-play"></i>
                                Xem chi tiết
                            </button>
                            <button class="btn btn-secondary" onclick="window.pages.home.bookMovie(${movie.id})">
                                <i class="fas fa-ticket-alt"></i>
                                Đặt vé
                            </button>
                        </div>
                    </div>
                    ${movie.rating ? `
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>${movie.rating}</span>
                        </div>
                    ` : ''}
                </div>
                
                <div class="movie-info">
                    <h3 class="movie-title">${movie.title}</h3>
                    <div class="movie-meta">
                        <span class="movie-genre">
                            <i class="fas fa-tag"></i>
                            ${movie.genre}
                        </span>
                        <span class="movie-duration">
                            <i class="fas fa-clock"></i>
                            ${movie.duration} phút
                        </span>
                    </div>
                    <p class="movie-description">
                        ${movie.description ? utils.truncateText(movie.description, 100) : 'Mô tả phim sẽ được cập nhật sớm...'}
                    </p>
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Genre filter buttons
        const filterBtns = utils.$$('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const genre = e.target.dataset.genre;
                this.filterByGenre(genre);
            });
        });

        // Search input
        const searchInput = utils.$('#movie-search');
        if (searchInput) {
            searchInput.addEventListener('input', utils.debounce(() => {
                this.searchQuery = searchInput.value.trim();
                this.filterMovies();
                this.updateDisplay();
            }, 300));
        }

        // Sort select
        const sortSelect = utils.$('#sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.currentSort = sortSelect.value;
                this.sortMovies();
                this.updateDisplay();
            });
        }

        // Movie card hover effects
        const movieCards = utils.$$('.movie-card');
        movieCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('hovered');
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('hovered');
            });
        });
    },

    filterByGenre(genre) {
        this.currentGenre = genre;
        this.filterMovies();
        this.updateDisplay();
    },

    filterMovies() {
        let filtered = [...this.movies];
        
        // Filter by genre
        if (this.currentGenre !== 'all') {
            filtered = filtered.filter(movie => movie.genre === this.currentGenre);
        }
        
        // Filter by search query
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(movie => 
                movie.title.toLowerCase().includes(query) ||
                movie.genre.toLowerCase().includes(query) ||
                (movie.description && movie.description.toLowerCase().includes(query))
            );
        }
        
        this.filteredMovies = filtered;
        this.sortMovies();
    },

    sortMovies() {
        switch (this.currentSort) {
            case 'rating':
                this.filteredMovies.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                break;
            case 'newest':
                this.filteredMovies.sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate));
                break;
            case 'title':
                this.filteredMovies.sort((a, b) => a.title.localeCompare(b.title));
                break;
            case 'popularity':
            default:
                // Keep original order or sort by some popularity metric
                break;
        }
    },

    updateDisplay() {
        // Re-render the movies section
        this.renderHomePage();
        this.setupEventHandlers();
    },

    getUniqueGenres() {
        const genres = [...new Set(this.movies.map(movie => movie.genre))];
        return genres.sort();
    },

    clearFilters() {
        this.currentGenre = 'all';
        this.searchQuery = '';
        this.filterMovies();
        this.updateDisplay();
    },

    scrollToMovies() {
        const moviesSection = utils.$('#movies-section');
        if (moviesSection) {
            moviesSection.scrollIntoView({ behavior: 'smooth' });
        }
    },

    viewMovieDetail(movieId) {
        window.router.navigate(`/movies/${movieId}`);
    },

    bookMovie(movieId) {
        // For now, redirect to movie detail page where user can select showtime
        window.router.navigate(`/movies/${movieId}`);
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải trang</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.pages.home.render()">
                                Thử lại
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
