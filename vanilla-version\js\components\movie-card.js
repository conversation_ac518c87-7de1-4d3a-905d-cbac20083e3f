// ===== MOVIE CARD COMPONENT =====

class MovieCard {
    constructor(movie, options = {}) {
        this.movie = movie;
        this.options = {
            showBookButton: true,
            showTrailerButton: true,
            showRating: true,
            showPrice: true,
            size: 'normal', // 'small', 'normal', 'large'
            ...options
        };
    }

    render() {
        const { movie, options } = this;
        const cardClass = `movie-card ${options.size === 'small' ? 'movie-card-small' : ''}`;
        
        return `
            <div class="${cardClass}" data-movie-id="${movie.id}">
                <div class="movie-poster-container">
                    <img src="${movie.poster || movie.posterUrl || '/assets/images/placeholder-movie.jpg'}" 
                         alt="${movie.title}" 
                         class="movie-poster"
                         onerror="this.src='/assets/images/placeholder-movie.jpg'">
                    
                    ${this.renderOverlay()}
                    ${this.renderBadges()}
                    ${options.showRating && movie.rating ? this.renderRating() : ''}
                </div>
                
                <div class="movie-info">
                    <h3 class="movie-title">${movie.title}</h3>
                    ${this.renderMeta()}
                    ${this.renderDescription()}
                    ${this.renderActions()}
                </div>
            </div>
        `;
    }

    renderOverlay() {
        const { movie, options } = this;
        
        if (!options.showTrailerButton && !options.showBookButton) {
            return '';
        }

        return `
            <div class="movie-overlay">
                <div class="movie-actions">
                    ${options.showTrailerButton ? `
                        <button class="btn btn-secondary btn-sm" onclick="MovieCard.watchTrailer(${movie.id})">
                            <i class="fas fa-play"></i>
                            Trailer
                        </button>
                    ` : ''}
                    ${options.showBookButton ? `
                        <button class="btn btn-primary btn-sm" onclick="MovieCard.bookTicket(${movie.id})">
                            <i class="fas fa-ticket-alt"></i>
                            Đặt vé
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    renderBadges() {
        const { movie } = this;
        let badges = '';

        // Age rating badge
        if (movie.ageRating) {
            const badgeClass = this.getAgeRatingClass(movie.ageRating);
            badges += `
                <div class="movie-badge age-rating ${badgeClass}">
                    ${movie.ageRating}
                </div>
            `;
        }

        // Status badge
        if (movie.status === 'coming-soon') {
            badges += `
                <div class="movie-badge status-badge coming-soon">
                    <i class="fas fa-calendar"></i>
                    Sắp chiếu
                </div>
            `;
        } else if (movie.status === 'hot') {
            badges += `
                <div class="movie-badge status-badge hot">
                    <i class="fas fa-fire"></i>
                    Hot
                </div>
            `;
        }

        return badges;
    }

    renderRating() {
        const { movie } = this;
        const rating = movie.rating || movie.averageRating || 0;
        
        return `
            <div class="movie-rating">
                <i class="fas fa-star"></i>
                <span>${rating.toFixed(1)}</span>
            </div>
        `;
    }

    renderMeta() {
        const { movie } = this;
        
        return `
            <div class="movie-meta">
                <span class="movie-genre">
                    <i class="fas fa-tag"></i>
                    ${movie.genre}
                </span>
                <span class="movie-duration">
                    <i class="fas fa-clock"></i>
                    ${movie.duration} phút
                </span>
                ${movie.releaseDate ? `
                    <span class="movie-release">
                        <i class="fas fa-calendar-alt"></i>
                        ${utils.formatDate(movie.releaseDate)}
                    </span>
                ` : ''}
            </div>
        `;
    }

    renderDescription() {
        const { movie, options } = this;
        
        if (!movie.description) return '';
        
        const maxLength = options.size === 'small' ? 60 : 100;
        const description = utils.truncateText(movie.description, maxLength);
        
        return `
            <p class="movie-description">${description}</p>
        `;
    }

    renderActions() {
        const { movie, options } = this;
        
        if (options.size === 'small') return '';
        
        return `
            <div class="movie-card-actions">
                ${options.showPrice ? `
                    <div class="movie-price">
                        <span class="price-label">Từ</span>
                        <span class="price-value">${utils.formatPrice(movie.price || 85000)}</span>
                    </div>
                ` : ''}
                
                <div class="action-buttons">
                    <button class="btn btn-outline btn-sm" onclick="MovieCard.viewDetail(${movie.id})">
                        <i class="fas fa-eye"></i>
                        Chi tiết
                    </button>
                    ${options.showBookButton ? `
                        <button class="btn btn-primary btn-sm" onclick="MovieCard.bookTicket(${movie.id})">
                            <i class="fas fa-ticket-alt"></i>
                            Đặt vé
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    getAgeRatingClass(ageRating) {
        switch (ageRating) {
            case '18+': return 'rating-18';
            case '16+': return 'rating-16';
            case '13+': return 'rating-13';
            case 'P': return 'rating-p';
            default: return 'rating-default';
        }
    }

    // Static methods for event handlers
    static viewDetail(movieId) {
        window.router.navigate(`/movies/${movieId}`);
    }

    static bookTicket(movieId) {
        // Navigate to movie detail page where user can select showtime
        window.router.navigate(`/movies/${movieId}#showtimes`);
    }

    static async watchTrailer(movieId) {
        try {
            // Try to get trailer URL from API
            const movie = await api.get(`/api/movies/${movieId}`);
            
            if (movie.trailerUrl) {
                // Open trailer in modal
                const modalContent = `
                    <div class="trailer-modal">
                        <div class="trailer-container">
                            <iframe 
                                src="${movie.trailerUrl}" 
                                frameborder="0" 
                                allowfullscreen
                                class="trailer-iframe">
                            </iframe>
                        </div>
                    </div>
                `;
                
                await Modal.show(modalContent, {
                    title: `Trailer - ${movie.title}`,
                    size: 'large',
                    showConfirm: false,
                    cancelText: 'Đóng'
                });
            } else {
                Toast.info('Trailer chưa có sẵn cho phim này');
            }
        } catch (error) {
            Toast.error('Không thể tải trailer');
        }
    }

    static async addToWishlist(movieId) {
        if (!auth.isAuthenticated()) {
            const shouldLogin = await Modal.confirm(
                'Bạn cần đăng nhập để thêm vào danh sách yêu thích. Đăng nhập ngay?',
                { title: 'Yêu cầu đăng nhập' }
            );
            
            if (shouldLogin) {
                window.router.navigate('/login');
            }
            return;
        }

        try {
            await api.post('/api/wishlist', { movieId });
            Toast.success('Đã thêm vào danh sách yêu thích');
        } catch (error) {
            Toast.error('Không thể thêm vào danh sách yêu thích');
        }
    }

    static renderGrid(movies, options = {}) {
        const gridOptions = {
            columns: 'auto-fit',
            minWidth: '280px',
            gap: '1.5rem',
            ...options
        };

        const movieCards = movies.map(movie => {
            const card = new MovieCard(movie, options);
            return card.render();
        }).join('');

        return `
            <div class="movies-grid" style="
                display: grid;
                grid-template-columns: repeat(${gridOptions.columns}, minmax(${gridOptions.minWidth}, 1fr));
                gap: ${gridOptions.gap};
            ">
                ${movieCards}
            </div>
        `;
    }

    static renderList(movies, options = {}) {
        const listOptions = {
            showIndex: false,
            ...options
        };

        const movieCards = movies.map((movie, index) => {
            const card = new MovieCard(movie, { ...options, size: 'small' });
            const cardHtml = card.render();
            
            if (listOptions.showIndex) {
                return `
                    <div class="movie-list-item">
                        <div class="movie-index">${index + 1}</div>
                        ${cardHtml}
                    </div>
                `;
            }
            
            return `<div class="movie-list-item">${cardHtml}</div>`;
        }).join('');

        return `
            <div class="movies-list">
                ${movieCards}
            </div>
        `;
    }
}

// Export to global scope
window.MovieCard = MovieCard;
