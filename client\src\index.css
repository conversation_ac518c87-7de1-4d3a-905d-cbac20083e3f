@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 3.7%, 15.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(0, 84.2%, 60.2%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* Cinema specific colors */
  --cinema-red: hsl(0, 84.2%, 60.2%);
  --cinema-dark: hsl(211, 39%, 9%);
  --cinema-gray: hsl(216, 17%, 17%);
  --cinema-light: hsl(210, 40%, 98%);
  --cinema-muted: hsl(216, 9%, 62%);
  --cinema-gold: hsl(43, 89%, 49%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background-color: var(--cinema-dark);
    color: var(--cinema-light);
  }

  .movie-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .movie-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  }

  .seat {
    transition: all 0.3s ease;
  }

  .seat:hover {
    transform: scale(1.1);
  }

  .seat.selected {
    background-color: var(--cinema-red);
    color: white;
  }

  .seat.occupied {
    background-color: var(--cinema-muted);
    cursor: not-allowed;
  }

  .navbar-scroll {
    backdrop-filter: blur(10px);
    background-color: rgba(17, 24, 39, 0.95);
  }
}

@layer components {
  .btn-primary {
    @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors;
  }

  .cinema-card {
    @apply bg-gray-800 rounded-xl overflow-hidden transition-all duration-300;
  }

  .cinema-card:hover {
    @apply transform -translate-y-2 shadow-2xl;
  }
}
