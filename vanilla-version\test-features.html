<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Features - NaCinema</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1f2937;
            color: white;
        }
        .test-section {
            background-color: #374151;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-button {
            background-color: #dc2626;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #b91c1c;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #065f46;
            color: #10b981;
        }
        .error {
            background-color: #7f1d1d;
            color: #ef4444;
        }
        .info {
            background-color: #1e3a8a;
            color: #60a5fa;
        }
    </style>
</head>
<body>
    <h1>🧪 NaCinema Feature Testing</h1>
    <p>Trang này để test các tính năng của phiên bản vanilla trước khi xóa code React.</p>

    <!-- Navigation Test -->
    <div class="test-section">
        <h2>🧭 Navigation & Routing</h2>
        <button class="test-button" onclick="testNavigation()">Test Navigation</button>
        <button class="test-button" onclick="testRouting()">Test Routing</button>
        <div id="nav-result" class="test-result"></div>
    </div>

    <!-- Authentication Test -->
    <div class="test-section">
        <h2>🔐 Authentication</h2>
        <button class="test-button" onclick="testAuth()">Test Auth System</button>
        <button class="test-button" onclick="testLogin()">Test Login</button>
        <button class="test-button" onclick="testLogout()">Test Logout</button>
        <div id="auth-result" class="test-result"></div>
    </div>

    <!-- API Test -->
    <div class="test-section">
        <h2>🌐 API & Data</h2>
        <button class="test-button" onclick="testAPI()">Test API Client</button>
        <button class="test-button" onclick="testMovieAPI()">Test Movie API</button>
        <button class="test-button" onclick="testCaching()">Test Caching</button>
        <div id="api-result" class="test-result"></div>
    </div>

    <!-- Components Test -->
    <div class="test-section">
        <h2>🧩 Components</h2>
        <button class="test-button" onclick="testModal()">Test Modal</button>
        <button class="test-button" onclick="testToast()">Test Toast</button>
        <button class="test-button" onclick="testSeatMap()">Test SeatMap</button>
        <div id="components-result" class="test-result"></div>
    </div>

    <!-- Pages Test -->
    <div class="test-section">
        <h2>📱 Pages</h2>
        <button class="test-button" onclick="testPages()">Test All Pages</button>
        <button class="test-button" onclick="testBookingFlow()">Test Booking Flow</button>
        <div id="pages-result" class="test-result"></div>
    </div>

    <!-- Performance Test -->
    <div class="test-section">
        <h2>⚡ Performance</h2>
        <button class="test-button" onclick="testPerformance()">Test Load Time</button>
        <button class="test-button" onclick="testMemory()">Test Memory Usage</button>
        <div id="performance-result" class="test-result"></div>
    </div>

    <!-- Responsive Test -->
    <div class="test-section">
        <h2>📱 Responsive Design</h2>
        <button class="test-button" onclick="testResponsive()">Test Mobile View</button>
        <button class="test-button" onclick="testTablet()">Test Tablet View</button>
        <div id="responsive-result" class="test-result"></div>
    </div>

    <!-- Overall Test -->
    <div class="test-section">
        <h2>🎯 Overall Test</h2>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button" onclick="generateReport()">Generate Report</button>
        <div id="overall-result" class="test-result"></div>
    </div>

    <script>
        // Test Results Storage
        const testResults = {
            navigation: [],
            auth: [],
            api: [],
            components: [],
            pages: [],
            performance: [],
            responsive: []
        };

        // Utility Functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function addResult(category, test, status, message) {
            testResults[category].push({
                test,
                status,
                message,
                timestamp: new Date().toISOString()
            });
        }

        // Navigation Tests
        function testNavigation() {
            showResult('nav-result', 'Testing navigation...', 'info');
            
            try {
                // Test if router exists
                if (typeof window.router === 'undefined') {
                    throw new Error('Router not found');
                }

                // Test navigation functions
                const tests = [
                    () => window.router.navigate('/'),
                    () => window.router.navigate('/login'),
                    () => window.router.back(),
                    () => window.router.getCurrentRoute()
                ];

                tests.forEach((test, index) => {
                    try {
                        test();
                        addResult('navigation', `Navigation Test ${index + 1}`, 'pass', 'OK');
                    } catch (error) {
                        addResult('navigation', `Navigation Test ${index + 1}`, 'fail', error.message);
                    }
                });

                showResult('nav-result', 'Navigation tests completed! ✅', 'success');
            } catch (error) {
                showResult('nav-result', `Navigation test failed: ${error.message}`, 'error');
                addResult('navigation', 'Navigation System', 'fail', error.message);
            }
        }

        function testRouting() {
            showResult('nav-result', 'Testing routing system...', 'info');
            
            try {
                const routes = ['/', '/login', '/register', '/movies/1'];
                let passedRoutes = 0;

                routes.forEach(route => {
                    try {
                        window.router.navigate(route);
                        passedRoutes++;
                        addResult('navigation', `Route ${route}`, 'pass', 'Navigated successfully');
                    } catch (error) {
                        addResult('navigation', `Route ${route}`, 'fail', error.message);
                    }
                });

                showResult('nav-result', `Routing test: ${passedRoutes}/${routes.length} routes working ✅`, 'success');
            } catch (error) {
                showResult('nav-result', `Routing test failed: ${error.message}`, 'error');
            }
        }

        // Authentication Tests
        function testAuth() {
            showResult('auth-result', 'Testing authentication system...', 'info');
            
            try {
                if (typeof window.auth === 'undefined') {
                    throw new Error('Auth system not found');
                }

                const authTests = [
                    () => window.auth.isAuthenticated(),
                    () => window.auth.getCurrentUser(),
                    () => window.auth.hasRole('user'),
                    () => window.auth.hasPermission('view:movies')
                ];

                let passedTests = 0;
                authTests.forEach((test, index) => {
                    try {
                        test();
                        passedTests++;
                        addResult('auth', `Auth Test ${index + 1}`, 'pass', 'OK');
                    } catch (error) {
                        addResult('auth', `Auth Test ${index + 1}`, 'fail', error.message);
                    }
                });

                showResult('auth-result', `Auth system: ${passedTests}/${authTests.length} tests passed ✅`, 'success');
            } catch (error) {
                showResult('auth-result', `Auth test failed: ${error.message}`, 'error');
            }
        }

        function testLogin() {
            showResult('auth-result', 'Testing login functionality...', 'info');
            
            // Test login page navigation
            try {
                window.router.navigate('/login');
                setTimeout(() => {
                    const loginForm = document.getElementById('login-form');
                    if (loginForm) {
                        showResult('auth-result', 'Login page loaded successfully ✅', 'success');
                        addResult('auth', 'Login Page', 'pass', 'Page rendered correctly');
                    } else {
                        showResult('auth-result', 'Login page not found ❌', 'error');
                        addResult('auth', 'Login Page', 'fail', 'Form not found');
                    }
                }, 1000);
            } catch (error) {
                showResult('auth-result', `Login test failed: ${error.message}`, 'error');
            }
        }

        function testLogout() {
            showResult('auth-result', 'Testing logout functionality...', 'info');
            
            try {
                if (window.auth.isAuthenticated()) {
                    window.auth.logout();
                    showResult('auth-result', 'Logout successful ✅', 'success');
                    addResult('auth', 'Logout', 'pass', 'User logged out');
                } else {
                    showResult('auth-result', 'No user logged in to test logout', 'info');
                    addResult('auth', 'Logout', 'skip', 'No user logged in');
                }
            } catch (error) {
                showResult('auth-result', `Logout test failed: ${error.message}`, 'error');
            }
        }

        // API Tests
        function testAPI() {
            showResult('api-result', 'Testing API client...', 'info');
            
            try {
                if (typeof window.api === 'undefined') {
                    throw new Error('API client not found');
                }

                // Test API methods
                const apiMethods = ['get', 'post', 'put', 'delete'];
                let availableMethods = 0;

                apiMethods.forEach(method => {
                    if (typeof window.api[method] === 'function') {
                        availableMethods++;
                        addResult('api', `API ${method.toUpperCase()}`, 'pass', 'Method available');
                    } else {
                        addResult('api', `API ${method.toUpperCase()}`, 'fail', 'Method not found');
                    }
                });

                showResult('api-result', `API client: ${availableMethods}/${apiMethods.length} methods available ✅`, 'success');
            } catch (error) {
                showResult('api-result', `API test failed: ${error.message}`, 'error');
            }
        }

        function testMovieAPI() {
            showResult('api-result', 'Testing Movie API...', 'info');
            
            try {
                if (typeof window.movieAPI === 'undefined') {
                    throw new Error('Movie API not found');
                }

                // Test movie API methods
                const movieMethods = ['getMovies', 'getMovie', 'getMovieShowtimes'];
                let availableMethods = 0;

                movieMethods.forEach(method => {
                    if (typeof window.movieAPI[method] === 'function') {
                        availableMethods++;
                        addResult('api', `Movie API ${method}`, 'pass', 'Method available');
                    } else {
                        addResult('api', `Movie API ${method}`, 'fail', 'Method not found');
                    }
                });

                showResult('api-result', `Movie API: ${availableMethods}/${movieMethods.length} methods available ✅`, 'success');
            } catch (error) {
                showResult('api-result', `Movie API test failed: ${error.message}`, 'error');
            }
        }

        function testCaching() {
            showResult('api-result', 'Testing caching system...', 'info');
            
            try {
                if (typeof window.queryCache === 'undefined') {
                    throw new Error('Query cache not found');
                }

                // Test cache methods
                const cacheMethods = ['query', 'setQueryData', 'invalidateQueries'];
                let availableMethods = 0;

                cacheMethods.forEach(method => {
                    if (typeof window.queryCache[method] === 'function') {
                        availableMethods++;
                        addResult('api', `Cache ${method}`, 'pass', 'Method available');
                    } else {
                        addResult('api', `Cache ${method}`, 'fail', 'Method not found');
                    }
                });

                showResult('api-result', `Caching: ${availableMethods}/${cacheMethods.length} methods available ✅`, 'success');
            } catch (error) {
                showResult('api-result', `Caching test failed: ${error.message}`, 'error');
            }
        }

        // Component Tests
        function testModal() {
            showResult('components-result', 'Testing Modal component...', 'info');
            
            try {
                if (typeof window.Modal === 'undefined') {
                    throw new Error('Modal component not found');
                }

                // Test modal methods
                const modalMethods = ['show', 'hide', 'confirm', 'alert'];
                let availableMethods = 0;

                modalMethods.forEach(method => {
                    if (typeof window.Modal[method] === 'function') {
                        availableMethods++;
                        addResult('components', `Modal ${method}`, 'pass', 'Method available');
                    } else {
                        addResult('components', `Modal ${method}`, 'fail', 'Method not found');
                    }
                });

                // Test modal functionality
                window.Modal.show('Test modal', { title: 'Test' });
                setTimeout(() => window.Modal.hide(), 1000);

                showResult('components-result', `Modal: ${availableMethods}/${modalMethods.length} methods available ✅`, 'success');
            } catch (error) {
                showResult('components-result', `Modal test failed: ${error.message}`, 'error');
            }
        }

        function testToast() {
            showResult('components-result', 'Testing Toast component...', 'info');
            
            try {
                if (typeof window.Toast === 'undefined') {
                    throw new Error('Toast component not found');
                }

                // Test toast methods
                const toastMethods = ['show', 'success', 'error', 'warning', 'info'];
                let availableMethods = 0;

                toastMethods.forEach(method => {
                    if (typeof window.Toast[method] === 'function') {
                        availableMethods++;
                        addResult('components', `Toast ${method}`, 'pass', 'Method available');
                    } else {
                        addResult('components', `Toast ${method}`, 'fail', 'Method not found');
                    }
                });

                // Test toast functionality
                window.Toast.success('Test toast notification');

                showResult('components-result', `Toast: ${availableMethods}/${toastMethods.length} methods available ✅`, 'success');
            } catch (error) {
                showResult('components-result', `Toast test failed: ${error.message}`, 'error');
            }
        }

        function testSeatMap() {
            showResult('components-result', 'Testing SeatMap component...', 'info');
            
            try {
                if (typeof window.SeatMap === 'undefined') {
                    throw new Error('SeatMap component not found');
                }

                addResult('components', 'SeatMap Class', 'pass', 'Component available');
                showResult('components-result', 'SeatMap component available ✅', 'success');
            } catch (error) {
                showResult('components-result', `SeatMap test failed: ${error.message}`, 'error');
                addResult('components', 'SeatMap Class', 'fail', error.message);
            }
        }

        // Pages Test
        function testPages() {
            showResult('pages-result', 'Testing all pages...', 'info');
            
            try {
                if (typeof window.pages === 'undefined') {
                    throw new Error('Pages object not found');
                }

                const pageNames = ['home', 'movieDetail', 'login', 'register', 'booking', 'bookingSuccess'];
                let availablePages = 0;

                pageNames.forEach(pageName => {
                    if (window.pages[pageName] && typeof window.pages[pageName].render === 'function') {
                        availablePages++;
                        addResult('pages', `Page ${pageName}`, 'pass', 'Page available');
                    } else {
                        addResult('pages', `Page ${pageName}`, 'fail', 'Page not found');
                    }
                });

                showResult('pages-result', `Pages: ${availablePages}/${pageNames.length} pages available ✅`, 'success');
            } catch (error) {
                showResult('pages-result', `Pages test failed: ${error.message}`, 'error');
            }
        }

        function testBookingFlow() {
            showResult('pages-result', 'Testing booking flow...', 'info');
            
            try {
                // Test booking flow navigation
                const bookingSteps = [
                    () => window.router.navigate('/'),
                    () => window.router.navigate('/movies/1'),
                    () => window.router.navigate('/booking/1'),
                    () => window.router.navigate('/booking/success/ABC123')
                ];

                let completedSteps = 0;
                bookingSteps.forEach((step, index) => {
                    try {
                        step();
                        completedSteps++;
                        addResult('pages', `Booking Step ${index + 1}`, 'pass', 'Navigation successful');
                    } catch (error) {
                        addResult('pages', `Booking Step ${index + 1}`, 'fail', error.message);
                    }
                });

                showResult('pages-result', `Booking flow: ${completedSteps}/${bookingSteps.length} steps working ✅`, 'success');
            } catch (error) {
                showResult('pages-result', `Booking flow test failed: ${error.message}`, 'error');
            }
        }

        // Performance Tests
        function testPerformance() {
            showResult('performance-result', 'Testing performance...', 'info');
            
            const startTime = performance.now();
            
            // Simulate some operations
            setTimeout(() => {
                const endTime = performance.now();
                const loadTime = endTime - startTime;
                
                if (loadTime < 1000) {
                    showResult('performance-result', `Load time: ${loadTime.toFixed(2)}ms ✅`, 'success');
                    addResult('performance', 'Load Time', 'pass', `${loadTime.toFixed(2)}ms`);
                } else {
                    showResult('performance-result', `Load time: ${loadTime.toFixed(2)}ms (slow) ⚠️`, 'error');
                    addResult('performance', 'Load Time', 'fail', `${loadTime.toFixed(2)}ms`);
                }
            }, 100);
        }

        function testMemory() {
            showResult('performance-result', 'Testing memory usage...', 'info');
            
            if (performance.memory) {
                const memory = performance.memory;
                const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
                
                showResult('performance-result', `Memory: ${usedMB}MB / ${totalMB}MB ✅`, 'success');
                addResult('performance', 'Memory Usage', 'pass', `${usedMB}MB used`);
            } else {
                showResult('performance-result', 'Memory API not available', 'info');
                addResult('performance', 'Memory Usage', 'skip', 'API not available');
            }
        }

        // Responsive Tests
        function testResponsive() {
            showResult('responsive-result', 'Testing mobile responsiveness...', 'info');
            
            const originalWidth = window.innerWidth;
            
            // Simulate mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375
            });
            
            window.dispatchEvent(new Event('resize'));
            
            setTimeout(() => {
                // Check if mobile styles are applied
                const isMobile = window.utils && window.utils.device && window.utils.device.isMobile();
                
                if (isMobile) {
                    showResult('responsive-result', 'Mobile view working ✅', 'success');
                    addResult('responsive', 'Mobile View', 'pass', 'Responsive design working');
                } else {
                    showResult('responsive-result', 'Mobile detection not working ❌', 'error');
                    addResult('responsive', 'Mobile View', 'fail', 'Mobile detection failed');
                }
                
                // Restore original width
                Object.defineProperty(window, 'innerWidth', {
                    writable: true,
                    configurable: true,
                    value: originalWidth
                });
                window.dispatchEvent(new Event('resize'));
            }, 500);
        }

        function testTablet() {
            showResult('responsive-result', 'Testing tablet responsiveness...', 'info');
            
            const originalWidth = window.innerWidth;
            
            // Simulate tablet viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 768
            });
            
            window.dispatchEvent(new Event('resize'));
            
            setTimeout(() => {
                const isTablet = window.utils && window.utils.device && window.utils.device.isTablet();
                
                if (isTablet) {
                    showResult('responsive-result', 'Tablet view working ✅', 'success');
                    addResult('responsive', 'Tablet View', 'pass', 'Responsive design working');
                } else {
                    showResult('responsive-result', 'Tablet detection not working ❌', 'error');
                    addResult('responsive', 'Tablet View', 'fail', 'Tablet detection failed');
                }
                
                // Restore original width
                Object.defineProperty(window, 'innerWidth', {
                    writable: true,
                    configurable: true,
                    value: originalWidth
                });
                window.dispatchEvent(new Event('resize'));
            }, 500);
        }

        // Overall Tests
        function runAllTests() {
            showResult('overall-result', 'Running all tests...', 'info');
            
            // Clear previous results
            Object.keys(testResults).forEach(key => {
                testResults[key] = [];
            });
            
            // Run all tests
            testNavigation();
            setTimeout(() => testAuth(), 500);
            setTimeout(() => testAPI(), 1000);
            setTimeout(() => testModal(), 1500);
            setTimeout(() => testToast(), 2000);
            setTimeout(() => testSeatMap(), 2500);
            setTimeout(() => testPages(), 3000);
            setTimeout(() => testPerformance(), 3500);
            setTimeout(() => testResponsive(), 4000);
            
            setTimeout(() => {
                generateReport();
            }, 5000);
        }

        function generateReport() {
            showResult('overall-result', 'Generating test report...', 'info');
            
            let totalTests = 0;
            let passedTests = 0;
            let failedTests = 0;
            let skippedTests = 0;
            
            Object.values(testResults).forEach(category => {
                category.forEach(result => {
                    totalTests++;
                    if (result.status === 'pass') passedTests++;
                    else if (result.status === 'fail') failedTests++;
                    else if (result.status === 'skip') skippedTests++;
                });
            });
            
            const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
            
            const report = `
                <h3>📊 Test Report</h3>
                <p><strong>Total Tests:</strong> ${totalTests}</p>
                <p><strong>Passed:</strong> ${passedTests} ✅</p>
                <p><strong>Failed:</strong> ${failedTests} ❌</p>
                <p><strong>Skipped:</strong> ${skippedTests} ⏭️</p>
                <p><strong>Success Rate:</strong> ${successRate}%</p>
                
                ${successRate >= 80 ? 
                    '<p class="success">🎉 Great! The vanilla version is ready for production!</p>' :
                    '<p class="error">⚠️ Some issues found. Please fix before removing React code.</p>'
                }
            `;
            
            showResult('overall-result', report, successRate >= 80 ? 'success' : 'error');
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            console.log('🧪 NaCinema Feature Testing Page Loaded');
            console.log('Click buttons to test individual features or run all tests');
        });
    </script>
</body>
</html>
