/* ===== RESPONSIVE DESIGN SYSTEM ===== */

/* Mobile First Approach - Base styles are for mobile */

/* ===== BREAKPOINTS ===== */
/* 
  xs: 0px - 479px (Mobile Portrait)
  sm: 480px - 767px (Mobile Landscape) 
  md: 768px - 1023px (Tablet)
  lg: 1024px - 1279px (Desktop)
  xl: 1280px+ (Large Desktop)
*/

/* ===== SMALL MOBILE (up to 479px) ===== */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--spacing-2);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
    }
    
    .hero-description {
        font-size: var(--font-size-base);
    }
    
    .movie-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .nav-container {
        padding: var(--spacing-3);
    }
    
    .search-input {
        width: 120px;
    }
    
    .search-input:focus {
        width: 140px;
    }
    
    .btn {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--font-size-sm);
    }
    
    .btn-lg {
        padding: var(--spacing-3) var(--spacing-6);
        font-size: var(--font-size-base);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .movie-card {
        margin-bottom: var(--spacing-4);
    }
    
    .hero-section {
        height: 40vh;
        min-height: 300px;
        padding: var(--spacing-4);
    }
    
    .hero-actions {
        gap: var(--spacing-2);
    }
    
    .filters-section {
        padding: var(--spacing-4) 0;
    }
    
    .footer {
        padding: var(--spacing-8) 0 var(--spacing-4);
    }
    
    .footer-content {
        gap: var(--spacing-4);
    }
}

/* ===== MOBILE LANDSCAPE (480px - 767px) ===== */
@media (min-width: 480px) and (max-width: 767px) {
    .movie-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-4);
    }
    
    .hero-actions {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .search-input {
        width: 160px;
    }
    
    .search-input:focus {
        width: 200px;
    }
    
    .filters-container {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .filter-group {
        flex-direction: row;
        min-width: 200px;
    }
}

/* ===== TABLET (768px - 1023px) ===== */
@media (min-width: 768px) and (max-width: 1023px) {
    .movie-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-5);
    }
    
    .nav-links {
        display: flex;
        gap: var(--spacing-4);
    }
    
    .hero-section {
        height: 60vh;
        min-height: 450px;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-actions {
        flex-direction: row;
        gap: var(--spacing-4);
    }
    
    .filters-container {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .section-header {
        flex-direction: row;
        align-items: center;
    }
    
    .search-input {
        width: 180px;
    }
    
    .search-input:focus {
        width: 220px;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ===== DESKTOP (1024px - 1279px) ===== */
@media (min-width: 1024px) and (max-width: 1279px) {
    .movie-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-6);
    }
    
    .hero-section {
        height: 70vh;
        min-height: 500px;
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
    
    .nav-links {
        gap: var(--spacing-6);
    }
    
    .search-input {
        width: 200px;
    }
    
    .search-input:focus {
        width: 250px;
    }
    
    .footer-content {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* ===== LARGE DESKTOP (1280px+) ===== */
@media (min-width: 1280px) {
    .movie-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-6);
    }
    
    .container {
        max-width: 1280px;
    }
    
    .hero-content {
        max-width: 700px;
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* ===== ULTRA WIDE (1440px+) ===== */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }
    
    .movie-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .hero-content {
        max-width: 800px;
    }
}

/* ===== ORIENTATION SPECIFIC ===== */
@media (orientation: landscape) and (max-height: 500px) {
    .hero-section {
        height: 80vh;
        min-height: 400px;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-description {
        font-size: var(--font-size-base);
    }
    
    .navbar {
        padding: var(--spacing-2) 0;
    }
    
    .nav-container {
        padding: var(--spacing-2) var(--spacing-4);
    }
}

/* ===== HIGH DPI DISPLAYS ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .movie-poster img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    .logo i {
        transform: translateZ(0);
    }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .spinner {
        animation: none;
    }
    
    .movie-card:hover {
        transform: none;
    }
    
    .movie-poster img {
        transition: none;
    }
    
    .play-button:hover {
        transform: none;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: light) {
    /* Light mode overrides if needed */
    :root {
        --gray-900: #f9fafb;
        --gray-800: #f3f4f6;
        --gray-700: #e5e7eb;
        --gray-600: #d1d5db;
        --gray-500: #9ca3af;
        --gray-400: #6b7280;
        --gray-300: #4b5563;
        --gray-200: #374151;
        --gray-100: #1f2937;
        --white: #111827;
        --black: #ffffff;
    }
    
    body {
        background-color: var(--gray-900);
        color: var(--gray-100);
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .footer,
    .hero-section,
    .filters-section,
    .mobile-menu,
    .user-dropdown,
    .modal-overlay,
    .toast-container {
        display: none !important;
    }
    
    .main-content {
        padding-top: 0;
    }
    
    .movie-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .card {
        border: 1px solid #ccc;
        box-shadow: none;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-contrast: high) {
    :root {
        --primary-red: #ff0000;
        --gray-400: #808080;
        --gray-600: #404040;
    }
    
    .nav-link:focus,
    .btn:focus,
    .form-input:focus {
        outline: 3px solid var(--primary-red);
        outline-offset: 2px;
    }
}

/* ===== HOVER SUPPORT ===== */
@media (hover: hover) {
    .movie-card:hover .movie-poster img {
        transform: scale(1.05);
    }
    
    .movie-card:hover .movie-play-overlay {
        opacity: 1;
    }
}

@media (hover: none) {
    .movie-play-overlay {
        opacity: 1;
    }
    
    .movie-card:hover {
        transform: none;
    }
}
