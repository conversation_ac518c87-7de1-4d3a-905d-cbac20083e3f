// ===== BOOKING PAGE =====

window.pages.booking = {
    currentBooking: null,
    paymentMethods: [
        { id: 'vnpay', name: 'VNP<PERSON>', icon: 'fas fa-credit-card' },
        { id: 'momo', name: '<PERSON><PERSON><PERSON>', icon: 'fas fa-mobile-alt' },
        { id: 'zalopay', name: 'Zalo<PERSON><PERSON>', icon: 'fas fa-wallet' },
        { id: 'banking', name: 'Internet Banking', icon: 'fas fa-university' }
    ],

    async render(showtimeId) {
        const mainContent = utils.$('#main-content');
        
        try {
            // Load booking data from storage
            this.currentBooking = utils.storage.get('pendingBooking');
            
            if (!this.currentBooking) {
                this.renderError('Không tìm thấy thông tin đặt vé. Vui lòng chọn lại ghế.');
                return;
            }

            // Render booking page
            this.renderBookingPage();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to render booking page:', error);
            this.renderError('Có lỗi xảy ra. Vui lòng thử lại.');
        }
    },

    renderBookingPage() {
        const mainContent = utils.$('#main-content');
        const booking = this.currentBooking;

        mainContent.innerHTML = `
            <div class="booking-page">
                <div class="container">
                    <div class="booking-header">
                        <button class="btn btn-secondary" id="back-btn">
                            <i class="fas fa-arrow-left"></i>
                            Quay lại
                        </button>
                        <h1 class="booking-title">Thanh toán</h1>
                        <div class="booking-timer">
                            <i class="fas fa-clock"></i>
                            <span id="countdown">15:00</span>
                        </div>
                    </div>

                    <div class="booking-content">
                        <div class="booking-main">
                            <!-- Booking Summary -->
                            <div class="booking-section">
                                <h2 class="section-title">Thông tin đặt vé</h2>
                                <div class="booking-summary">
                                    <div class="movie-info">
                                        <img src="${booking.movie.poster}" alt="${booking.movie.title}" class="movie-poster-small">
                                        <div class="movie-details">
                                            <h3 class="movie-title">${booking.movie.title}</h3>
                                            <div class="showtime-info">
                                                <p><i class="fas fa-calendar"></i> ${utils.formatDate(booking.showtime.date)}</p>
                                                <p><i class="fas fa-clock"></i> ${utils.formatTime(booking.showtime.startTime)}</p>
                                                <p><i class="fas fa-map-marker-alt"></i> ${booking.showtime.cinema.name} - Phòng ${booking.showtime.room.name}</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="seats-info">
                                        <h4>Ghế đã chọn:</h4>
                                        <div class="selected-seats">
                                            ${booking.seats.map(seat => `
                                                <span class="seat-badge">${seat}</span>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="booking-section">
                                <h2 class="section-title">Thông tin khách hàng</h2>
                                <form id="customer-form" class="customer-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">Họ và tên *</label>
                                            <input type="text" name="fullName" class="form-input" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Số điện thoại *</label>
                                            <input type="tel" name="phone" class="form-input" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <input type="email" name="email" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Ghi chú</label>
                                        <textarea name="notes" class="form-input" rows="3" placeholder="Ghi chú thêm (không bắt buộc)"></textarea>
                                    </div>
                                </form>
                            </div>

                            <!-- Payment Method -->
                            <div class="booking-section">
                                <h2 class="section-title">Phương thức thanh toán</h2>
                                <div class="payment-methods" id="payment-methods">
                                    ${this.paymentMethods.map(method => `
                                        <label class="payment-method">
                                            <input type="radio" name="paymentMethod" value="${method.id}" ${method.id === 'vnpay' ? 'checked' : ''}>
                                            <div class="payment-option">
                                                <i class="${method.icon}"></i>
                                                <span>${method.name}</span>
                                            </div>
                                        </label>
                                    `).join('')}
                                </div>
                            </div>

                            <!-- Promotion Code -->
                            <div class="booking-section">
                                <h2 class="section-title">Mã khuyến mãi</h2>
                                <div class="promotion-form">
                                    <div class="promo-input-group">
                                        <input type="text" id="promo-code" class="form-input" placeholder="Nhập mã khuyến mãi">
                                        <button type="button" class="btn btn-secondary" id="apply-promo">
                                            Áp dụng
                                        </button>
                                    </div>
                                    <div id="promo-result" class="promo-result"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Price Summary -->
                        <div class="booking-sidebar">
                            <div class="price-summary">
                                <h3>Tổng thanh toán</h3>
                                
                                <div class="price-breakdown">
                                    <div class="price-item">
                                        <span>Vé xem phim (${booking.seats.length} ghế)</span>
                                        <span id="ticket-price">${utils.formatPrice(booking.totalPrice)}</span>
                                    </div>
                                    <div class="price-item" id="service-fee-item">
                                        <span>Phí dịch vụ</span>
                                        <span id="service-fee">10.000đ</span>
                                    </div>
                                    <div class="price-item discount-item" id="discount-item" style="display: none;">
                                        <span>Giảm giá</span>
                                        <span id="discount-amount">-0đ</span>
                                    </div>
                                    <div class="price-divider"></div>
                                    <div class="price-total">
                                        <span>Tổng cộng</span>
                                        <span id="total-price">${utils.formatPrice(booking.totalPrice + 10000)}</span>
                                    </div>
                                </div>

                                <button class="btn btn-primary btn-lg btn-block" id="pay-btn">
                                    <i class="fas fa-credit-card"></i>
                                    Thanh toán
                                </button>

                                <div class="payment-security">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Thanh toán được bảo mật bởi SSL</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Back button
        const backBtn = utils.$('#back-btn');
        backBtn.addEventListener('click', () => {
            window.router.navigate(`/movies/${this.currentBooking.movie.id}`);
        });

        // Payment button
        const payBtn = utils.$('#pay-btn');
        payBtn.addEventListener('click', () => {
            this.handlePayment();
        });

        // Promo code
        const applyPromoBtn = utils.$('#apply-promo');
        applyPromoBtn.addEventListener('click', () => {
            this.applyPromoCode();
        });

        // Form validation
        const form = utils.$('#customer-form');
        const inputs = form.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });

        // Start countdown timer
        this.startCountdown();

        // Auto-fill user info if logged in
        if (auth.isAuthenticated()) {
            this.fillUserInfo();
        }
    },

    async handlePayment() {
        const form = utils.$('#customer-form');
        const formData = new FormData(form);
        const paymentMethod = utils.$('input[name="paymentMethod"]:checked').value;

        // Validate form
        if (!this.validateForm()) {
            Toast.error('Vui lòng điền đầy đủ thông tin bắt buộc');
            return;
        }

        const bookingData = {
            ...this.currentBooking,
            customer: {
                fullName: formData.get('fullName'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                notes: formData.get('notes')
            },
            paymentMethod,
            totalAmount: this.calculateTotal()
        };

        try {
            // Show loading
            this.setPaymentLoading(true);

            // Create booking
            const response = await bookingAPI.createBooking(bookingData);

            // Clear pending booking
            utils.storage.remove('pendingBooking');

            // Redirect to success page
            window.router.navigate(`/booking/success/${response.bookingCode}`);

        } catch (error) {
            console.error('Payment failed:', error);
            Toast.error('Thanh toán thất bại. Vui lòng thử lại.');
        } finally {
            this.setPaymentLoading(false);
        }
    },

    async applyPromoCode() {
        const promoCode = utils.$('#promo-code').value.trim();
        const resultDiv = utils.$('#promo-result');

        if (!promoCode) {
            Toast.warning('Vui lòng nhập mã khuyến mãi');
            return;
        }

        try {
            const result = await promotionAPI.validatePromotion(promoCode, this.calculateTotal());
            
            if (result.valid) {
                resultDiv.innerHTML = `
                    <div class="promo-success">
                        <i class="fas fa-check-circle"></i>
                        Áp dụng thành công! Giảm ${utils.formatPrice(result.discount)}
                    </div>
                `;
                this.updatePricing(result.discount);
                Toast.success('Áp dụng mã khuyến mãi thành công!');
            } else {
                resultDiv.innerHTML = `
                    <div class="promo-error">
                        <i class="fas fa-times-circle"></i>
                        ${result.message || 'Mã khuyến mãi không hợp lệ'}
                    </div>
                `;
            }
        } catch (error) {
            resultDiv.innerHTML = `
                <div class="promo-error">
                    <i class="fas fa-times-circle"></i>
                    Không thể áp dụng mã khuyến mãi
                </div>
            `;
        }
    },

    validateForm() {
        const form = utils.$('#customer-form');
        const requiredInputs = form.querySelectorAll('input[required]');
        let isValid = true;

        requiredInputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    },

    validateField(input) {
        const value = input.value.trim();
        let isValid = true;
        let errorMessage = '';

        if (!value) {
            errorMessage = 'Trường này là bắt buộc';
            isValid = false;
        } else {
            switch (input.type) {
                case 'tel':
                    if (!utils.validators.phone(value)) {
                        errorMessage = 'Số điện thoại không hợp lệ';
                        isValid = false;
                    }
                    break;
                case 'email':
                    if (value && !utils.validators.email(value)) {
                        errorMessage = 'Email không hợp lệ';
                        isValid = false;
                    }
                    break;
            }
        }

        // Show/hide error
        let errorDiv = input.parentNode.querySelector('.field-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            input.parentNode.appendChild(errorDiv);
        }

        if (isValid) {
            input.classList.remove('error');
            errorDiv.style.display = 'none';
        } else {
            input.classList.add('error');
            errorDiv.textContent = errorMessage;
            errorDiv.style.display = 'block';
        }

        return isValid;
    },

    calculateTotal() {
        const basePrice = this.currentBooking.totalPrice;
        const serviceFee = 10000;
        const discount = this.getCurrentDiscount();
        return basePrice + serviceFee - discount;
    },

    getCurrentDiscount() {
        const discountItem = utils.$('#discount-item');
        if (discountItem.style.display === 'none') return 0;
        
        const discountText = utils.$('#discount-amount').textContent;
        return parseInt(discountText.replace(/[^\d]/g, '')) || 0;
    },

    updatePricing(discount = 0) {
        const discountItem = utils.$('#discount-item');
        const discountAmount = utils.$('#discount-amount');
        const totalPrice = utils.$('#total-price');

        if (discount > 0) {
            discountItem.style.display = 'flex';
            discountAmount.textContent = `-${utils.formatPrice(discount)}`;
        } else {
            discountItem.style.display = 'none';
        }

        totalPrice.textContent = utils.formatPrice(this.calculateTotal());
    },

    fillUserInfo() {
        const user = auth.getCurrentUser();
        if (user) {
            const form = utils.$('#customer-form');
            if (user.fullName) form.fullName.value = user.fullName;
            if (user.phone) form.phone.value = user.phone;
            if (user.email) form.email.value = user.email;
        }
    },

    startCountdown() {
        let timeLeft = 15 * 60; // 15 minutes
        const countdownEl = utils.$('#countdown');

        const timer = setInterval(() => {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            
            countdownEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                this.handleTimeout();
            }
            
            timeLeft--;
        }, 1000);
    },

    handleTimeout() {
        Modal.alert('Thời gian giữ ghế đã hết. Vui lòng chọn lại ghế.', {
            title: 'Hết thời gian'
        }).then(() => {
            utils.storage.remove('pendingBooking');
            window.router.navigate('/');
        });
    },

    setPaymentLoading(loading) {
        const payBtn = utils.$('#pay-btn');
        
        if (loading) {
            payBtn.disabled = true;
            payBtn.innerHTML = `
                <div class="spinner"></div>
                Đang xử lý...
            `;
        } else {
            payBtn.disabled = false;
            payBtn.innerHTML = `
                <i class="fas fa-credit-card"></i>
                Thanh toán
            `;
        }
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi đặt vé</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <a href="#" class="btn btn-primary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
