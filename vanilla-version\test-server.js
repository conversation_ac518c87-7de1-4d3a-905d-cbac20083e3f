#!/usr/bin/env node

/**
 * Server Testing Script
 * 
 * Tests the backend API endpoints to ensure they work with the vanilla frontend
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

class ServerTester {
    constructor() {
        this.baseUrl = 'http://localhost:5000'; // Default backend URL
        this.testResults = [];
        this.timeout = 5000; // 5 seconds timeout
    }

    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',    // Cyan
            success: '\x1b[32m', // Green
            warning: '\x1b[33m', // Yellow
            error: '\x1b[31m',   // Red
            reset: '\x1b[0m'     // Reset
        };
        
        console.log(`${colors[type]}[${type.toUpperCase()}] ${message}${colors.reset}`);
    }

    async makeRequest(method, endpoint, data = null, headers = {}) {
        return new Promise((resolve, reject) => {
            const url = new URL(endpoint, this.baseUrl);
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                },
                timeout: this.timeout
            };

            const req = http.request(url, options, (res) => {
                let body = '';
                res.on('data', chunk => body += chunk);
                res.on('end', () => {
                    try {
                        const jsonBody = body ? JSON.parse(body) : {};
                        resolve({
                            status: res.statusCode,
                            headers: res.headers,
                            body: jsonBody
                        });
                    } catch (error) {
                        resolve({
                            status: res.statusCode,
                            headers: res.headers,
                            body: body
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            if (data) {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    addResult(test, status, message, details = null) {
        this.testResults.push({
            test,
            status,
            message,
            details,
            timestamp: new Date().toISOString()
        });
    }

    async testServerHealth() {
        this.log('🏥 Testing server health...');
        
        try {
            const response = await this.makeRequest('GET', '/api/health');
            
            if (response.status === 200) {
                this.log('✅ Server is healthy', 'success');
                this.addResult('Server Health', 'pass', 'Server responding correctly');
                return true;
            } else {
                this.log(`❌ Server health check failed: ${response.status}`, 'error');
                this.addResult('Server Health', 'fail', `HTTP ${response.status}`);
                return false;
            }
        } catch (error) {
            this.log(`❌ Cannot connect to server: ${error.message}`, 'error');
            this.addResult('Server Health', 'fail', error.message);
            return false;
        }
    }

    async testMovieAPI() {
        this.log('🎬 Testing Movie API...');
        
        const tests = [
            {
                name: 'Get Movies',
                method: 'GET',
                endpoint: '/api/movies',
                expectedStatus: 200
            },
            {
                name: 'Get Movie by ID',
                method: 'GET',
                endpoint: '/api/movies/1',
                expectedStatus: 200
            },
            {
                name: 'Get Movie Showtimes',
                method: 'GET',
                endpoint: '/api/movies/1/showtimes',
                expectedStatus: 200
            },
            {
                name: 'Search Movies',
                method: 'GET',
                endpoint: '/api/movies/search?q=test',
                expectedStatus: 200
            }
        ];

        let passedTests = 0;
        
        for (const test of tests) {
            try {
                const response = await this.makeRequest(test.method, test.endpoint);
                
                if (response.status === test.expectedStatus) {
                    this.log(`✅ ${test.name}`, 'success');
                    this.addResult(`Movie API - ${test.name}`, 'pass', 'API working correctly');
                    passedTests++;
                } else {
                    this.log(`❌ ${test.name}: Expected ${test.expectedStatus}, got ${response.status}`, 'error');
                    this.addResult(`Movie API - ${test.name}`, 'fail', `HTTP ${response.status}`);
                }
            } catch (error) {
                this.log(`❌ ${test.name}: ${error.message}`, 'error');
                this.addResult(`Movie API - ${test.name}`, 'fail', error.message);
            }
        }
        
        this.log(`Movie API: ${passedTests}/${tests.length} tests passed`);
        return passedTests === tests.length;
    }

    async testAuthAPI() {
        this.log('🔐 Testing Authentication API...');
        
        const tests = [
            {
                name: 'Register User',
                method: 'POST',
                endpoint: '/api/auth/register',
                data: {
                    email: '<EMAIL>',
                    password: 'password123',
                    fullName: 'Test User'
                },
                expectedStatus: [201, 409] // 201 for success, 409 if user exists
            },
            {
                name: 'Login User',
                method: 'POST',
                endpoint: '/api/auth/login',
                data: {
                    email: '<EMAIL>',
                    password: 'password123'
                },
                expectedStatus: [200, 401] // 200 for success, 401 for invalid credentials
            },
            {
                name: 'Get User Profile',
                method: 'GET',
                endpoint: '/api/auth/profile',
                expectedStatus: [200, 401] // 200 if authenticated, 401 if not
            }
        ];

        let passedTests = 0;
        let authToken = null;
        
        for (const test of tests) {
            try {
                const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
                const response = await this.makeRequest(test.method, test.endpoint, test.data, headers);
                
                // Store auth token from login
                if (test.name === 'Login User' && response.status === 200 && response.body.token) {
                    authToken = response.body.token;
                }
                
                const expectedStatuses = Array.isArray(test.expectedStatus) ? test.expectedStatus : [test.expectedStatus];
                
                if (expectedStatuses.includes(response.status)) {
                    this.log(`✅ ${test.name}`, 'success');
                    this.addResult(`Auth API - ${test.name}`, 'pass', 'API working correctly');
                    passedTests++;
                } else {
                    this.log(`❌ ${test.name}: Expected ${expectedStatuses.join(' or ')}, got ${response.status}`, 'error');
                    this.addResult(`Auth API - ${test.name}`, 'fail', `HTTP ${response.status}`);
                }
            } catch (error) {
                this.log(`❌ ${test.name}: ${error.message}`, 'error');
                this.addResult(`Auth API - ${test.name}`, 'fail', error.message);
            }
        }
        
        this.log(`Auth API: ${passedTests}/${tests.length} tests passed`);
        return passedTests === tests.length;
    }

    async testBookingAPI() {
        this.log('🎫 Testing Booking API...');
        
        const tests = [
            {
                name: 'Get Showtimes',
                method: 'GET',
                endpoint: '/api/showtimes',
                expectedStatus: 200
            },
            {
                name: 'Get Showtime Seats',
                method: 'GET',
                endpoint: '/api/showtimes/1/seats',
                expectedStatus: 200
            },
            {
                name: 'Create Booking',
                method: 'POST',
                endpoint: '/api/bookings',
                data: {
                    showtimeId: 1,
                    seats: ['A1', 'A2'],
                    customer: {
                        fullName: 'Test User',
                        phone: '0123456789',
                        email: '<EMAIL>'
                    }
                },
                expectedStatus: [201, 400, 401] // Various possible responses
            },
            {
                name: 'Get Booking by Code',
                method: 'GET',
                endpoint: '/api/bookings/TEST123',
                expectedStatus: [200, 404] // 200 if found, 404 if not found
            }
        ];

        let passedTests = 0;
        
        for (const test of tests) {
            try {
                const response = await this.makeRequest(test.method, test.endpoint, test.data);
                
                const expectedStatuses = Array.isArray(test.expectedStatus) ? test.expectedStatus : [test.expectedStatus];
                
                if (expectedStatuses.includes(response.status)) {
                    this.log(`✅ ${test.name}`, 'success');
                    this.addResult(`Booking API - ${test.name}`, 'pass', 'API working correctly');
                    passedTests++;
                } else {
                    this.log(`❌ ${test.name}: Expected ${expectedStatuses.join(' or ')}, got ${response.status}`, 'error');
                    this.addResult(`Booking API - ${test.name}`, 'fail', `HTTP ${response.status}`);
                }
            } catch (error) {
                this.log(`❌ ${test.name}: ${error.message}`, 'error');
                this.addResult(`Booking API - ${test.name}`, 'fail', error.message);
            }
        }
        
        this.log(`Booking API: ${passedTests}/${tests.length} tests passed`);
        return passedTests === tests.length;
    }

    async testCORS() {
        this.log('🌐 Testing CORS configuration...');
        
        try {
            const response = await this.makeRequest('OPTIONS', '/api/movies', null, {
                'Origin': 'http://localhost:3000',
                'Access-Control-Request-Method': 'GET'
            });
            
            const corsHeaders = response.headers['access-control-allow-origin'];
            
            if (corsHeaders) {
                this.log('✅ CORS configured correctly', 'success');
                this.addResult('CORS Configuration', 'pass', 'CORS headers present');
                return true;
            } else {
                this.log('⚠️ CORS headers not found', 'warning');
                this.addResult('CORS Configuration', 'warning', 'CORS headers missing');
                return false;
            }
        } catch (error) {
            this.log(`❌ CORS test failed: ${error.message}`, 'error');
            this.addResult('CORS Configuration', 'fail', error.message);
            return false;
        }
    }

    async testDatabase() {
        this.log('🗄️ Testing database connection...');
        
        try {
            const response = await this.makeRequest('GET', '/api/health/db');
            
            if (response.status === 200) {
                this.log('✅ Database connection working', 'success');
                this.addResult('Database Connection', 'pass', 'Database accessible');
                return true;
            } else {
                this.log(`❌ Database connection failed: ${response.status}`, 'error');
                this.addResult('Database Connection', 'fail', `HTTP ${response.status}`);
                return false;
            }
        } catch (error) {
            this.log(`❌ Database test failed: ${error.message}`, 'error');
            this.addResult('Database Connection', 'fail', error.message);
            return false;
        }
    }

    generateReport() {
        this.log('📊 Generating test report...');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.status === 'pass').length;
        const failedTests = this.testResults.filter(r => r.status === 'fail').length;
        const warningTests = this.testResults.filter(r => r.status === 'warning').length;
        
        const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                warnings: warningTests,
                successRate: `${successRate}%`
            },
            results: this.testResults
        };
        
        // Save report to file
        const reportPath = path.join(__dirname, `server-test-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // Display summary
        console.log('\n' + '='.repeat(50));
        console.log('📊 SERVER TEST REPORT');
        console.log('='.repeat(50));
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests} ✅`);
        console.log(`Failed: ${failedTests} ❌`);
        console.log(`Warnings: ${warningTests} ⚠️`);
        console.log(`Success Rate: ${successRate}%`);
        console.log(`Report saved: ${reportPath}`);
        console.log('='.repeat(50));
        
        if (successRate >= 80) {
            this.log('🎉 Server is ready for production!', 'success');
        } else {
            this.log('⚠️ Some issues found. Please fix before deployment.', 'warning');
        }
        
        return report;
    }

    async run() {
        this.log('🚀 Starting server tests...', 'info');
        
        try {
            // Test server health first
            const serverHealthy = await this.testServerHealth();
            if (!serverHealthy) {
                this.log('❌ Server is not responding. Please start the backend server first.', 'error');
                return false;
            }
            
            // Run all tests
            await this.testMovieAPI();
            await this.testAuthAPI();
            await this.testBookingAPI();
            await this.testCORS();
            await this.testDatabase();
            
            // Generate report
            const report = this.generateReport();
            
            return report.summary.successRate >= 80;
            
        } catch (error) {
            this.log(`❌ Test execution failed: ${error.message}`, 'error');
            return false;
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new ServerTester();
    
    // Allow custom base URL
    if (process.argv[2]) {
        tester.baseUrl = process.argv[2];
        console.log(`Using custom server URL: ${tester.baseUrl}`);
    }
    
    tester.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = ServerTester;
