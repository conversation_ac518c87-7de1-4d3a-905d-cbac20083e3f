#!/usr/bin/env node

/**
 * Migration Script: React to Vanilla HTML/CSS/JavaScript
 * 
 * This script safely migrates from React version to vanilla version
 * by backing up React code and cleaning up unnecessary files.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class VanillaMigrator {
    constructor() {
        this.rootDir = path.resolve(__dirname, '..');
        this.vanillaDir = path.resolve(__dirname);
        this.backupDir = path.resolve(this.rootDir, 'react-backup');
        this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        this.reactFiles = [
            'src/',
            'public/',
            'package.json',
            'package-lock.json',
            'yarn.lock',
            'node_modules/',
            '.gitignore',
            'README.md',
            'tsconfig.json',
            'vite.config.js',
            'vite.config.ts',
            'tailwind.config.js',
            'postcss.config.js'
        ];
        
        this.keepFiles = [
            'server/',
            'backend/',
            'api/',
            'database/',
            'docs/',
            '.git/',
            'vanilla-version/',
            'test-features.html',
            'migrate-to-vanilla.js'
        ];
    }

    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',    // Cyan
            success: '\x1b[32m', // Green
            warning: '\x1b[33m', // Yellow
            error: '\x1b[31m',   // Red
            reset: '\x1b[0m'     // Reset
        };
        
        console.log(`${colors[type]}[${type.toUpperCase()}] ${message}${colors.reset}`);
    }

    async checkPrerequisites() {
        this.log('🔍 Checking prerequisites...');
        
        // Check if vanilla version exists
        if (!fs.existsSync(this.vanillaDir)) {
            throw new Error('Vanilla version directory not found!');
        }
        
        // Check if vanilla version has required files
        const requiredFiles = [
            'index.html',
            'css/main.css',
            'css/components.css',
            'js/app.js',
            'js/router.js',
            'js/api.js'
        ];
        
        for (const file of requiredFiles) {
            const filePath = path.join(this.vanillaDir, file);
            if (!fs.existsSync(filePath)) {
                throw new Error(`Required vanilla file not found: ${file}`);
            }
        }
        
        this.log('✅ Prerequisites check passed');
    }

    async runTests() {
        this.log('🧪 Running feature tests...');
        
        try {
            // Check if test file exists
            const testFile = path.join(this.vanillaDir, 'test-features.html');
            if (!fs.existsSync(testFile)) {
                this.log('⚠️ Test file not found, skipping automated tests', 'warning');
                return true;
            }
            
            this.log('📝 Please manually run tests by opening test-features.html in browser');
            this.log('🔗 File location: ' + testFile);
            
            // Wait for user confirmation
            const readline = require('readline').createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            return new Promise((resolve) => {
                readline.question('Have you run the tests and confirmed everything works? (y/N): ', (answer) => {
                    readline.close();
                    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                        this.log('✅ Tests confirmed by user');
                        resolve(true);
                    } else {
                        this.log('❌ Tests not confirmed, aborting migration', 'error');
                        resolve(false);
                    }
                });
            });
        } catch (error) {
            this.log(`❌ Test execution failed: ${error.message}`, 'error');
            return false;
        }
    }

    async createBackup() {
        this.log('💾 Creating backup of React code...');
        
        // Create backup directory
        const backupPath = `${this.backupDir}-${this.timestamp}`;
        fs.mkdirSync(backupPath, { recursive: true });
        
        // Copy React files to backup
        for (const file of this.reactFiles) {
            const sourcePath = path.join(this.rootDir, file);
            const targetPath = path.join(backupPath, file);
            
            if (fs.existsSync(sourcePath)) {
                try {
                    if (fs.statSync(sourcePath).isDirectory()) {
                        this.copyDirectory(sourcePath, targetPath);
                    } else {
                        fs.mkdirSync(path.dirname(targetPath), { recursive: true });
                        fs.copyFileSync(sourcePath, targetPath);
                    }
                    this.log(`📁 Backed up: ${file}`);
                } catch (error) {
                    this.log(`⚠️ Failed to backup ${file}: ${error.message}`, 'warning');
                }
            }
        }
        
        // Create backup info file
        const backupInfo = {
            timestamp: this.timestamp,
            originalPath: this.rootDir,
            vanillaPath: this.vanillaDir,
            backedUpFiles: this.reactFiles.filter(file => 
                fs.existsSync(path.join(this.rootDir, file))
            ),
            migrationVersion: '1.0.0'
        };
        
        fs.writeFileSync(
            path.join(backupPath, 'backup-info.json'),
            JSON.stringify(backupInfo, null, 2)
        );
        
        this.log(`✅ Backup created: ${backupPath}`, 'success');
        return backupPath;
    }

    copyDirectory(source, target) {
        fs.mkdirSync(target, { recursive: true });
        
        const files = fs.readdirSync(source);
        for (const file of files) {
            const sourcePath = path.join(source, file);
            const targetPath = path.join(target, file);
            
            if (fs.statSync(sourcePath).isDirectory()) {
                this.copyDirectory(sourcePath, targetPath);
            } else {
                fs.copyFileSync(sourcePath, targetPath);
            }
        }
    }

    async moveVanillaToRoot() {
        this.log('📦 Moving vanilla files to root...');
        
        // Get all files in vanilla directory
        const vanillaFiles = this.getAllFiles(this.vanillaDir);
        
        for (const file of vanillaFiles) {
            const relativePath = path.relative(this.vanillaDir, file);
            
            // Skip the migration script itself
            if (relativePath === 'migrate-to-vanilla.js') continue;
            
            const targetPath = path.join(this.rootDir, relativePath);
            
            // Create target directory if needed
            fs.mkdirSync(path.dirname(targetPath), { recursive: true });
            
            // Copy file
            fs.copyFileSync(file, targetPath);
            this.log(`📄 Moved: ${relativePath}`);
        }
        
        this.log('✅ Vanilla files moved to root', 'success');
    }

    getAllFiles(dir) {
        const files = [];
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            if (fs.statSync(fullPath).isDirectory()) {
                files.push(...this.getAllFiles(fullPath));
            } else {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    async cleanupReactFiles() {
        this.log('🧹 Cleaning up React files...');
        
        for (const file of this.reactFiles) {
            const filePath = path.join(this.rootDir, file);
            
            if (fs.existsSync(filePath)) {
                try {
                    if (fs.statSync(filePath).isDirectory()) {
                        fs.rmSync(filePath, { recursive: true, force: true });
                    } else {
                        fs.unlinkSync(filePath);
                    }
                    this.log(`🗑️ Removed: ${file}`);
                } catch (error) {
                    this.log(`⚠️ Failed to remove ${file}: ${error.message}`, 'warning');
                }
            }
        }
        
        this.log('✅ React files cleaned up', 'success');
    }

    async updatePackageJson() {
        this.log('📝 Creating new package.json for vanilla version...');
        
        const newPackageJson = {
            name: "nacinema-vanilla",
            version: "1.0.0",
            description: "NaCinema - Movie Ticket Booking System (Vanilla Version)",
            main: "index.html",
            scripts: {
                "start": "npx http-server . -p 3000 -o",
                "dev": "npx live-server --port=3000 --open=/",
                "build": "echo 'No build step needed for vanilla version'",
                "test": "echo 'Open test-features.html in browser to run tests'"
            },
            devDependencies: {
                "http-server": "^14.1.1",
                "live-server": "^1.2.2"
            },
            keywords: ["cinema", "movie", "booking", "vanilla", "javascript"],
            author: "NaCinema Team",
            license: "MIT"
        };
        
        fs.writeFileSync(
            path.join(this.rootDir, 'package.json'),
            JSON.stringify(newPackageJson, null, 2)
        );
        
        this.log('✅ New package.json created', 'success');
    }

    async createReadme() {
        this.log('📖 Creating README for vanilla version...');
        
        const readme = `# NaCinema - Vanilla Version

## 🎬 Movie Ticket Booking System

This is the vanilla HTML/CSS/JavaScript version of NaCinema, converted from React.

### ✨ Features

- 🎥 Movie browsing and details
- 🎫 Seat selection and booking
- 👤 User authentication
- 💳 Payment processing
- 📱 Responsive design
- 🎨 Modern UI/UX

### 🚀 Quick Start

1. **Install dependencies:**
   \`\`\`bash
   npm install
   \`\`\`

2. **Start development server:**
   \`\`\`bash
   npm run dev
   \`\`\`

3. **Open in browser:**
   \`\`\`
   http://localhost:3000
   \`\`\`

### 🧪 Testing

Open \`test-features.html\` in your browser to run feature tests.

### 📁 Project Structure

\`\`\`
├── index.html          # Main HTML file
├── css/                # Stylesheets
│   ├── main.css        # Main styles
│   ├── components.css  # Component styles
│   └── responsive.css  # Responsive styles
├── js/                 # JavaScript files
│   ├── app.js          # Main application
│   ├── router.js       # Client-side routing
│   ├── api.js          # API client
│   ├── auth.js         # Authentication
│   ├── utils.js        # Utilities
│   ├── components/     # UI components
│   └── pages/          # Page components
└── test-features.html  # Feature testing page
\`\`\`

### 🔧 Development

- **No build step required** - just edit files and refresh browser
- **Live reload** available with \`npm run dev\`
- **Modern JavaScript** (ES6+) used throughout
- **Modular architecture** for maintainability

### 🎯 Migration Notes

This project was successfully migrated from React to vanilla JavaScript on ${new Date().toLocaleDateString()}.

- ✅ All React components converted to vanilla JS
- ✅ State management implemented with vanilla JS
- ✅ Routing system created from scratch
- ✅ All functionality preserved
- ✅ Performance optimized
- ✅ Bundle size reduced significantly

### 📞 Support

For support, please contact the development team or check the documentation.

---

**NaCinema Team** - Making movie booking simple and enjoyable! 🍿
`;

        fs.writeFileSync(path.join(this.rootDir, 'README.md'), readme);
        this.log('✅ README.md created', 'success');
    }

    async run() {
        try {
            this.log('🚀 Starting React to Vanilla migration...', 'info');
            
            // Step 1: Check prerequisites
            await this.checkPrerequisites();
            
            // Step 2: Run tests
            const testsPass = await this.runTests();
            if (!testsPass) {
                this.log('❌ Migration aborted due to test failures', 'error');
                return false;
            }
            
            // Step 3: Create backup
            const backupPath = await this.createBackup();
            
            // Step 4: Move vanilla files to root
            await this.moveVanillaToRoot();
            
            // Step 5: Clean up React files
            await this.cleanupReactFiles();
            
            // Step 6: Update package.json
            await this.updatePackageJson();
            
            // Step 7: Create new README
            await this.createReadme();
            
            // Step 8: Remove vanilla directory
            fs.rmSync(this.vanillaDir, { recursive: true, force: true });
            this.log('🗑️ Removed vanilla-version directory');
            
            this.log('🎉 Migration completed successfully!', 'success');
            this.log(`📦 React code backed up to: ${backupPath}`, 'info');
            this.log('🚀 You can now run: npm run dev', 'info');
            
            return true;
            
        } catch (error) {
            this.log(`❌ Migration failed: ${error.message}`, 'error');
            this.log('💡 Please check the error and try again', 'warning');
            return false;
        }
    }
}

// Run migration if called directly
if (require.main === module) {
    const migrator = new VanillaMigrator();
    migrator.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = VanillaMigrator;
