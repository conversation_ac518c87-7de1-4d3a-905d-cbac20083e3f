/* ===== NAVIGATION COMPONENT ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background-color: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-800);
  transition: all var(--transition-fast);
}

.navbar.scrolled {
  background-color: var(--gray-900);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--primary-red);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  text-decoration: none;
}

.logo i {
  font-size: var(--font-size-2xl);
}

.nav-links {
  display: flex;
  gap: var(--spacing-6);
}

.nav-link {
  color: var(--gray-400);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) 0;
  position: relative;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-red);
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-red);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

/* Search Component */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-800);
  border: 1px solid var(--gray-600);
  border-radius: var(--radius-md);
  color: var(--white);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-red);
  width: 250px;
}

.search-btn {
  position: absolute;
  right: var(--spacing-2);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
}

.search-btn:hover {
  color: var(--primary-red);
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-red);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.user-avatar:hover {
  background-color: var(--primary-red-hover);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-2);
  background-color: var(--gray-800);
  border: 1px solid var(--gray-700);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  min-width: 200px;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-300);
  text-decoration: none;
  transition: background-color var(--transition-fast);
}

.user-dropdown-item:hover {
  background-color: var(--gray-700);
  color: var(--white);
}

.user-dropdown-divider {
  height: 1px;
  background-color: var(--gray-700);
  margin: var(--spacing-2) 0;
}

/* Mobile Menu */
.mobile-menu-toggle {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-2);
}

.mobile-menu {
  display: none;
  background-color: var(--gray-800);
  border-top: 1px solid var(--gray-700);
  padding: var(--spacing-4);
}

.mobile-menu.show {
  display: block;
  animation: slideInDown var(--transition-fast);
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.mobile-nav-link {
  color: var(--gray-300);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-700);
  transition: color var(--transition-fast);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--primary-red);
}

/* ===== MOVIE CARD COMPONENT ===== */
.movie-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
  padding: var(--spacing-6) 0;
}

.movie-card {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.movie-poster {
  position: relative;
  aspect-ratio: 2/3;
  overflow: hidden;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.movie-card:hover .movie-poster img {
  transform: scale(1.05);
}

.movie-status-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  z-index: 10;
}

.movie-play-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.movie-card:hover .movie-play-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-red);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-2xl);
  transition: transform var(--transition-fast);
}

.play-button:hover {
  transform: scale(1.1);
}

.movie-info {
  padding: var(--spacing-4);
}

.movie-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--white);
  line-height: 1.3;
}

.movie-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

.movie-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.movie-rating i {
  color: #fbbf24;
}

.movie-genre {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--gray-700);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.movie-description {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: var(--spacing-4);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.movie-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* ===== HERO SECTION ===== */
.hero-section {
  position: relative;
  height: 70vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
}

.hero-background::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-content {
  position: relative;
  z-index: 10;
  max-width: 600px;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: 1.1;
  margin-bottom: var(--spacing-6);
}

.hero-title .highlight {
  color: var(--primary-red);
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--gray-300);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
}

.hero-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

/* ===== FILTERS SECTION ===== */
.filters-section {
  background-color: var(--gray-800);
  padding: var(--spacing-6) 0;
  border-bottom: 1px solid var(--gray-700);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-4);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-weight: var(--font-weight-medium);
  color: var(--gray-300);
  white-space: nowrap;
}

.filter-select {
  min-width: 150px;
}

/* ===== SECTION HEADERS ===== */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-8) 0 var(--spacing-4);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* ===== LOADING STATES ===== */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--gray-800) 25%,
    var(--gray-700) 50%,
    var(--gray-800) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-card {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.skeleton-poster {
  aspect-ratio: 2/3;
  background: var(--gray-700);
}

.skeleton-content {
  padding: var(--spacing-4);
}

.skeleton-title {
  height: 20px;
  background: var(--gray-700);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-3);
}

.skeleton-text {
  height: 14px;
  background: var(--gray-700);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-2);
}

.skeleton-text.short {
  width: 60%;
}

/* ===== FOOTER COMPONENT ===== */
.footer {
  background-color: var(--gray-900);
  border-top: 1px solid var(--gray-800);
  padding: var(--spacing-16) 0 var(--spacing-8);
  margin-top: var(--spacing-16);
}

.footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.footer-section h4 {
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: var(--spacing-2);
}

.footer-section a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-section a:hover {
  color: var(--primary-red);
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray-800);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-400);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background-color: var(--primary-red);
  color: var(--white);
  transform: translateY(-2px);
}

.contact-info li {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-400);
}

.contact-info i {
  width: 16px;
  color: var(--primary-red);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--spacing-8);
  text-align: center;
  color: var(--gray-400);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .hero-section {
    height: 50vh;
    min-height: 400px;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-description {
    font-size: var(--font-size-lg);
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-4);
  }

  .search-input {
    width: 150px;
  }

  .search-input:focus {
    width: 180px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }
}
