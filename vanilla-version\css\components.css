/* ===== NAVIGATION COMPONENT ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background-color: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-800);
  transition: all var(--transition-fast);
}

.navbar.scrolled {
  background-color: var(--gray-900);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--primary-red);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  text-decoration: none;
}

.logo i {
  font-size: var(--font-size-2xl);
}

.nav-links {
  display: flex;
  gap: var(--spacing-6);
}

.nav-link {
  color: var(--gray-400);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) 0;
  position: relative;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-red);
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-red);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

/* Search Component */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-800);
  border: 1px solid var(--gray-600);
  border-radius: var(--radius-md);
  color: var(--white);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-red);
  width: 250px;
}

.search-btn {
  position: absolute;
  right: var(--spacing-2);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
}

.search-btn:hover {
  color: var(--primary-red);
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-red);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.user-avatar:hover {
  background-color: var(--primary-red-hover);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-2);
  background-color: var(--gray-800);
  border: 1px solid var(--gray-700);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  min-width: 200px;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-300);
  text-decoration: none;
  transition: background-color var(--transition-fast);
}

.user-dropdown-item:hover {
  background-color: var(--gray-700);
  color: var(--white);
}

.user-dropdown-divider {
  height: 1px;
  background-color: var(--gray-700);
  margin: var(--spacing-2) 0;
}

/* Mobile Menu */
.mobile-menu-toggle {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-2);
}

.mobile-menu {
  display: none;
  background-color: var(--gray-800);
  border-top: 1px solid var(--gray-700);
  padding: var(--spacing-4);
}

.mobile-menu.show {
  display: block;
  animation: slideInDown var(--transition-fast);
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.mobile-nav-link {
  color: var(--gray-300);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-700);
  transition: color var(--transition-fast);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--primary-red);
}

/* ===== MOVIE CARD COMPONENT ===== */
.movie-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
  padding: var(--spacing-6) 0;
}

.movie-card {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.movie-poster {
  position: relative;
  aspect-ratio: 2/3;
  overflow: hidden;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.movie-card:hover .movie-poster img {
  transform: scale(1.05);
}

.movie-status-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  z-index: 10;
}

.movie-play-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.movie-card:hover .movie-play-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-red);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-2xl);
  transition: transform var(--transition-fast);
}

.play-button:hover {
  transform: scale(1.1);
}

.movie-info {
  padding: var(--spacing-4);
}

.movie-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--white);
  line-height: 1.3;
}

.movie-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

.movie-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.movie-rating i {
  color: #fbbf24;
}

.movie-genre {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--gray-700);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.movie-description {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: var(--spacing-4);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.movie-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* ===== HERO SECTION ===== */
.hero-section {
  position: relative;
  height: 70vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
}

.hero-background::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-content {
  position: relative;
  z-index: 10;
  max-width: 600px;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: 1.1;
  margin-bottom: var(--spacing-6);
}

.hero-title .highlight {
  color: var(--primary-red);
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--gray-300);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
}

.hero-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

/* ===== FILTERS SECTION ===== */
.filters-section {
  background-color: var(--gray-800);
  padding: var(--spacing-6) 0;
  border-bottom: 1px solid var(--gray-700);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-4);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-weight: var(--font-weight-medium);
  color: var(--gray-300);
  white-space: nowrap;
}

.filter-select {
  min-width: 150px;
}

/* ===== SECTION HEADERS ===== */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-8) 0 var(--spacing-4);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* ===== LOADING STATES ===== */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--gray-800) 25%,
    var(--gray-700) 50%,
    var(--gray-800) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-card {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.skeleton-poster {
  aspect-ratio: 2/3;
  background: var(--gray-700);
}

.skeleton-content {
  padding: var(--spacing-4);
}

.skeleton-title {
  height: 20px;
  background: var(--gray-700);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-3);
}

.skeleton-text {
  height: 14px;
  background: var(--gray-700);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-2);
}

.skeleton-text.short {
  width: 60%;
}

/* ===== FOOTER COMPONENT ===== */
.footer {
  background-color: var(--gray-900);
  border-top: 1px solid var(--gray-800);
  padding: var(--spacing-16) 0 var(--spacing-8);
  margin-top: var(--spacing-16);
}

.footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.footer-section h4 {
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: var(--spacing-2);
}

.footer-section a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-section a:hover {
  color: var(--primary-red);
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray-800);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-400);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background-color: var(--primary-red);
  color: var(--white);
  transform: translateY(-2px);
}

.contact-info li {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-400);
}

.contact-info i {
  width: 16px;
  color: var(--primary-red);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--spacing-8);
  text-align: center;
  color: var(--gray-400);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .hero-section {
    height: 50vh;
    min-height: 400px;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-description {
    font-size: var(--font-size-lg);
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-4);
  }

  .search-input {
    width: 150px;
  }

  .search-input:focus {
    width: 180px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }
}

/* ===== MODAL COMPONENT ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.modal-overlay.show {
  opacity: 1;
}

.modal-dialog {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-2xl);
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform var(--transition-fast);
  margin: var(--spacing-4);
}

.modal-overlay.show .modal-dialog {
  transform: scale(1);
}

.modal-small {
  width: 100%;
  max-width: 400px;
}

.modal-medium {
  width: 100%;
  max-width: 600px;
}

.modal-large {
  width: 100%;
  max-width: 800px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-700);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--gray-400);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  color: var(--white);
  background-color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-6);
  max-height: 70vh;
  overflow-y: auto;
}

/* Modal Content Types */
.confirm-modal,
.alert-modal,
.prompt-modal {
  text-align: center;
}

.confirm-message,
.alert-message,
.prompt-message {
  font-size: var(--font-size-base);
  color: var(--gray-300);
  margin-bottom: var(--spacing-6);
  line-height: 1.6;
}

.confirm-actions,
.alert-actions,
.prompt-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
}

.prompt-modal #prompt-input {
  width: 100%;
  margin-bottom: var(--spacing-4);
}

.loading-modal {
  text-align: center;
  padding: var(--spacing-4);
}

.loading-modal .spinner {
  margin: 0 auto var(--spacing-4);
}

.loading-message {
  color: var(--gray-300);
  font-size: var(--font-size-base);
}

/* ===== TOAST COMPONENT ===== */
.toast-container {
  position: fixed;
  z-index: var(--z-toast);
  pointer-events: none;
}

.toast-container.toast-top-right {
  top: var(--spacing-4);
  right: var(--spacing-4);
}

.toast-container.toast-top-left {
  top: var(--spacing-4);
  left: var(--spacing-4);
}

.toast-container.toast-bottom-right {
  bottom: var(--spacing-4);
  right: var(--spacing-4);
}

.toast-container.toast-bottom-left {
  bottom: var(--spacing-4);
  left: var(--spacing-4);
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-3);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--gray-600);
  min-width: 300px;
  max-width: 500px;
  pointer-events: auto;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--transition-fast);
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast.hide {
  opacity: 0;
  transform: translateX(100%);
}

.toast-success {
  border-left-color: #10b981;
}

.toast-error {
  border-left-color: var(--primary-red);
}

.toast-warning {
  border-left-color: #f59e0b;
}

.toast-info {
  border-left-color: #3b82f6;
}

.toast-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-success .toast-icon {
  color: #10b981;
}

.toast-error .toast-icon {
  color: var(--primary-red);
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

.toast-content {
  flex: 1;
}

.toast-message {
  color: var(--white);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.toast-close {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.toast-close:hover {
  color: var(--white);
  background-color: var(--gray-700);
}

/* Toast animations for different positions */
.toast-container.toast-top-left .toast,
.toast-container.toast-bottom-left .toast {
  transform: translateX(-100%);
}

.toast-container.toast-top-left .toast.show,
.toast-container.toast-bottom-left .toast.show {
  transform: translateX(0);
}

.toast-container.toast-top-left .toast.hide,
.toast-container.toast-bottom-left .toast.hide {
  transform: translateX(-100%);
}

/* ===== SEAT MAP COMPONENT ===== */
.seatmap-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

.screen-container {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.screen {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: var(--white);
  padding: var(--spacing-3) var(--spacing-8);
  border-radius: var(--radius-xl);
  display: inline-block;
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-lg);
  position: relative;
}

.screen::before {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #4f46e5;
}

.seatmap-grid {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.seat-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-3);
  gap: var(--spacing-2);
}

.row-label {
  width: 30px;
  text-align: center;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-400);
  margin-right: var(--spacing-4);
}

.seat {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  border: 2px solid transparent;
}

.seat:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.3);
}

.seat-number {
  font-size: 10px;
  font-weight: var(--font-weight-bold);
}

/* Seat States */
.seat-available {
  background-color: var(--gray-600);
  color: var(--gray-200);
}

.seat-available:hover {
  background-color: var(--gray-500);
  transform: scale(1.1);
}

.seat-selected {
  background-color: var(--primary-red);
  color: var(--white);
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.seat-reserved {
  background-color: var(--gray-700);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.seat-disabled {
  background-color: transparent;
  cursor: not-allowed;
  opacity: 0.3;
}

/* Seat Types */
.seat-vip {
  background-color: #f59e0b;
  color: var(--white);
}

.seat-vip:hover {
  background-color: #d97706;
}

.seat-vip.seat-selected {
  background-color: #92400e;
}

.seat-couple {
  background-color: #ec4899;
  color: var(--white);
  width: 68px; /* Double width for couple seats */
}

.seat-couple:hover {
  background-color: #db2777;
}

.seat-couple.seat-selected {
  background-color: #be185d;
}

.seatmap-legend {
  display: flex;
  justify-content: center;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
  font-size: var(--font-size-sm);
}

.seat-demo {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.selection-summary {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-seats-info {
  color: var(--gray-300);
  font-size: var(--font-size-base);
}

.selected-count {
  color: var(--primary-red);
  font-weight: var(--font-weight-semibold);
}

.total-price {
  color: var(--white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.price-amount {
  color: var(--primary-red);
}

.seatmap-loading,
.seatmap-error {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-300);
}

.seatmap-loading .spinner {
  margin: 0 auto var(--spacing-4);
}

.seatmap-error i {
  font-size: var(--font-size-3xl);
  color: var(--primary-red);
  margin-bottom: var(--spacing-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .seatmap-container {
    padding: var(--spacing-4);
  }

  .seat {
    width: 28px;
    height: 28px;
    font-size: 8px;
  }

  .seat-couple {
    width: 60px;
  }

  .seatmap-legend {
    gap: var(--spacing-3);
  }

  .selection-summary {
    flex-direction: column;
    gap: var(--spacing-2);
    text-align: center;
  }

  .row-label {
    width: 25px;
    margin-right: var(--spacing-2);
  }
}

/* ===== MOVIE DETAIL PAGE ===== */
.movie-detail-page {
  min-height: 100vh;
}

.movie-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.movie-hero-background {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.movie-hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.movie-hero-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(17, 24, 39, 0.9) 0%,
    rgba(17, 24, 39, 0.7) 50%,
    rgba(17, 24, 39, 0.9) 100%
  );
}

.movie-hero-content {
  position: relative;
  z-index: 10;
  display: flex;
  gap: var(--spacing-8);
  align-items: flex-start;
  padding: var(--spacing-8) 0;
}

.movie-poster-large {
  position: relative;
  flex-shrink: 0;
  width: 300px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
}

.movie-poster-large img {
  width: 100%;
  height: auto;
  display: block;
}

.movie-status-overlay {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
}

.movie-info-main {
  flex: 1;
  color: var(--white);
}

.movie-title-large {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  margin-bottom: var(--spacing-4);
  line-height: 1.2;
}

.movie-meta-large {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  color: var(--gray-300);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-base);
}

.meta-item i {
  color: var(--primary-red);
  width: 16px;
}

.movie-description-large {
  font-size: var(--font-size-lg);
  line-height: 1.7;
  color: var(--gray-200);
  margin-bottom: var(--spacing-8);
  max-width: 600px;
}

.movie-actions-large {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

/* Showtimes Section */
.showtimes-section {
  margin: var(--spacing-16) 0;
}

.showtimes-container {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.showtime-date-group {
  margin-bottom: var(--spacing-8);
}

.showtime-date-group:last-child {
  margin-bottom: 0;
}

.showtime-date {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: 2px solid var(--gray-700);
}

.showtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-4);
}

.showtime-btn {
  background-color: var(--gray-700);
  border: 2px solid var(--gray-600);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
  color: var(--white);
}

.showtime-btn:hover {
  border-color: var(--primary-red);
  background-color: var(--gray-600);
  transform: translateY(-2px);
}

.showtime-btn.active {
  border-color: var(--primary-red);
  background-color: rgba(220, 38, 38, 0.1);
}

.showtime-time {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-red);
  margin-bottom: var(--spacing-2);
}

.showtime-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-3);
}

.cinema-name {
  font-weight: var(--font-weight-medium);
  color: var(--white);
}

.room-name {
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

.showtime-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-red);
}

.no-showtimes {
  text-align: center;
  padding: var(--spacing-16);
  color: var(--gray-400);
}

.no-showtimes i {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
  color: var(--gray-600);
}

/* Booking Section */
.booking-section {
  margin: var(--spacing-16) 0;
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
}

.showtime-info {
  background-color: var(--gray-700);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
}

.selected-showtime {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.showtime-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.showtime-details strong {
  color: var(--primary-red);
  font-size: var(--font-size-lg);
}

.showtime-details span {
  color: var(--gray-300);
  font-size: var(--font-size-sm);
}

.seatmap-wrapper {
  margin: var(--spacing-8) 0;
}

.booking-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-4);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--gray-700);
}

/* Movie Tabs */
.movie-tabs {
  margin: var(--spacing-16) 0;
}

.tab-nav {
  display: flex;
  border-bottom: 2px solid var(--gray-700);
  margin-bottom: var(--spacing-6);
}

.tab-btn {
  background: none;
  border: none;
  padding: var(--spacing-4) var(--spacing-6);
  color: var(--gray-400);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
}

.tab-btn:hover {
  color: var(--white);
}

.tab-btn.active {
  color: var(--primary-red);
  border-bottom-color: var(--primary-red);
}

.tab-content {
  min-height: 300px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
  animation: fadeIn var(--transition-fast);
}

.movie-overview h3,
.cast-section h3,
.reviews-section h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin-bottom: var(--spacing-6);
}

.movie-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-3);
  background-color: var(--gray-800);
  border-radius: var(--radius-md);
}

.detail-item strong {
  color: var(--gray-300);
  font-weight: var(--font-weight-medium);
}

.detail-item span {
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .movie-hero-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-6);
  }

  .movie-poster-large {
    width: 250px;
    margin: 0 auto;
  }

  .movie-title-large {
    font-size: var(--font-size-3xl);
  }

  .movie-meta-large {
    justify-content: center;
    gap: var(--spacing-4);
  }

  .movie-actions-large {
    justify-content: center;
  }

  .showtime-grid {
    grid-template-columns: 1fr;
  }

  .booking-actions {
    flex-direction: column;
  }

  .tab-nav {
    overflow-x: auto;
    white-space: nowrap;
  }

  .movie-details-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== AUTHENTICATION PAGES ===== */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
  padding: var(--spacing-4);
}

.auth-container {
  width: 100%;
  max-width: 500px;
}

.auth-card {
  background-color: var(--gray-800);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--gray-700);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.auth-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
  color: var(--primary-red);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
}

.auth-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.auth-header p {
  color: var(--gray-400);
  font-size: var(--font-size-base);
}

.auth-form {
  margin-bottom: var(--spacing-6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.password-input-group {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--white);
}

.password-strength {
  margin-top: var(--spacing-2);
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: var(--gray-700);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-1);
}

.strength-fill {
  height: 100%;
  width: 0%;
  background-color: var(--gray-600);
  transition: all var(--transition-fast);
  border-radius: var(--radius-full);
}

.strength-text {
  font-size: var(--font-size-xs);
  color: var(--gray-400);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-600);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary-red);
  border-color: var(--primary-red);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: 12px;
  font-weight: var(--font-weight-bold);
}

.forgot-password {
  color: var(--primary-red);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.forgot-password:hover {
  color: var(--primary-red-hover);
}

.auth-submit {
  width: 100%;
  margin-bottom: var(--spacing-6);
  position: relative;
  overflow: hidden;
}

.btn-spinner {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-spinner .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.auth-divider {
  text-align: center;
  margin: var(--spacing-6) 0;
  position: relative;
}

.auth-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--gray-700);
}

.auth-divider span {
  background-color: var(--gray-800);
  color: var(--gray-400);
  padding: 0 var(--spacing-4);
  font-size: var(--font-size-sm);
  position: relative;
  z-index: 1;
}

.social-login {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.btn-social {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  border: 1px solid var(--gray-600);
  background-color: transparent;
  color: var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.btn-social:hover {
  border-color: var(--gray-500);
  background-color: var(--gray-700);
  color: var(--white);
}

.btn-google:hover {
  border-color: #db4437;
  background-color: rgba(219, 68, 55, 0.1);
  color: #db4437;
}

.btn-facebook:hover {
  border-color: #4267b2;
  background-color: rgba(66, 103, 178, 0.1);
  color: #4267b2;
}

.auth-footer {
  text-align: center;
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

.auth-link {
  color: var(--primary-red);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.auth-link:hover {
  color: var(--primary-red-hover);
}

.form-input.error {
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-error {
  display: none;
  color: var(--primary-red);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

/* Terms and Privacy Modal Content */
.terms-content,
.privacy-content {
  color: var(--gray-300);
  line-height: 1.6;
}

.terms-content h3,
.privacy-content h3 {
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-card {
    padding: var(--spacing-6);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-options {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }

  .social-login {
    grid-template-columns: 1fr;
  }

  .auth-header h2 {
    font-size: var(--font-size-2xl);
  }
}

/* ===== BOOKING PAGE ===== */
.booking-page {
  min-height: 100vh;
  background-color: var(--gray-900);
  padding: var(--spacing-4) 0;
}

.booking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-4) 0;
  border-bottom: 1px solid var(--gray-700);
}

.booking-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin: 0;
}

.booking-timer {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--primary-red);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

.booking-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: var(--spacing-8);
}

.booking-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.booking-section {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--gray-700);
}

.booking-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.movie-info {
  display: flex;
  gap: var(--spacing-4);
}

.movie-poster-small {
  width: 80px;
  height: 120px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.movie-details h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.showtime-info p {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
  margin-bottom: var(--spacing-1);
}

.showtime-info i {
  color: var(--primary-red);
  width: 16px;
}

.seats-info h4 {
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.selected-seats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.seat-badge {
  background-color: var(--primary-red);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.customer-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.payment-method {
  cursor: pointer;
}

.payment-method input[type="radio"] {
  display: none;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border: 2px solid var(--gray-600);
  border-radius: var(--radius-lg);
  background-color: var(--gray-700);
  transition: all var(--transition-fast);
}

.payment-method input[type="radio"]:checked + .payment-option {
  border-color: var(--primary-red);
  background-color: rgba(220, 38, 38, 0.1);
}

.payment-option i {
  font-size: var(--font-size-xl);
  color: var(--primary-red);
}

.payment-option span {
  color: var(--white);
  font-weight: var(--font-weight-medium);
}

.promo-input-group {
  display: flex;
  gap: var(--spacing-2);
}

.promo-input-group .form-input {
  flex: 1;
}

.promo-result {
  margin-top: var(--spacing-3);
}

.promo-success {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: #10b981;
  font-size: var(--font-size-sm);
}

.promo-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--primary-red);
  font-size: var(--font-size-sm);
}

.booking-sidebar {
  position: sticky;
  top: var(--spacing-4);
  height: fit-content;
}

.price-summary {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.price-summary h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-4);
  text-align: center;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--gray-300);
}

.price-item.discount-item {
  color: #10b981;
}

.price-divider {
  height: 1px;
  background-color: var(--gray-700);
  margin: var(--spacing-2) 0;
}

.price-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--white);
}

.price-total span:last-child {
  color: var(--primary-red);
}

.btn-block {
  width: 100%;
  margin: var(--spacing-4) 0;
}

.payment-security {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  color: var(--gray-400);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-4);
}

.payment-security i {
  color: #10b981;
}

.field-error {
  color: var(--primary-red);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
  display: none;
}

/* ===== BOOKING SUCCESS PAGE ===== */
.booking-success-page {
  min-height: 100vh;
  background-color: var(--gray-900);
  padding: var(--spacing-8) 0;
}

.success-content {
  max-width: 800px;
  margin: 0 auto;
}

.success-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.success-icon {
  font-size: 4rem;
  color: #10b981;
  margin-bottom: var(--spacing-4);
}

.success-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.success-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-300);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.booking-details-card {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-8);
}

.card-header {
  background-color: var(--gray-700);
  padding: var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin: 0;
}

.booking-code {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
}

.booking-code strong {
  color: var(--primary-red);
  font-family: monospace;
  font-size: var(--font-size-lg);
}

.copy-btn {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.copy-btn:hover {
  color: var(--white);
}

.booking-info {
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.movie-section {
  display: flex;
  gap: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--gray-700);
}

.movie-poster {
  width: 100px;
  height: 150px;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.movie-details h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.movie-meta p {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
  margin-bottom: var(--spacing-1);
}

.movie-meta i {
  color: var(--primary-red);
  width: 16px;
}

.showtime-section h4,
.seats-section h4,
.customer-section h4,
.payment-section h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-3);
}

.showtime-details,
.customer-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
}

.detail-item i {
  color: var(--primary-red);
  width: 16px;
}

.booked-seats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--gray-300);
}

.payment-item.total {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  padding-top: var(--spacing-2);
  border-top: 1px solid var(--gray-700);
}

.payment-item .amount {
  color: var(--primary-red);
}

.status-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-completed {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.status-failed {
  background-color: rgba(220, 38, 38, 0.2);
  color: var(--primary-red);
}

.qr-section {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.qr-section h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.qr-container {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.qr-code {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-4);
}

.qr-placeholder {
  width: 200px;
  height: 200px;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--gray-900);
}

.qr-placeholder i {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
}

.qr-note {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
  flex-wrap: wrap;
}

.important-notes {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.important-notes h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.important-notes i {
  color: var(--primary-red);
}

.important-notes ul {
  list-style: none;
  padding: 0;
}

.important-notes li {
  color: var(--gray-300);
  margin-bottom: var(--spacing-2);
  padding-left: var(--spacing-4);
  position: relative;
}

.important-notes li::before {
  content: "•";
  color: var(--primary-red);
  position: absolute;
  left: 0;
}

.navigation-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--gray-600);
  color: var(--gray-300);
}

.btn-outline:hover {
  background-color: var(--gray-700);
  border-color: var(--gray-500);
  color: var(--white);
}

/* ===== DASHBOARD PAGE ===== */
.dashboard-page {
  min-height: 100vh;
  background-color: var(--gray-900);
  padding: var(--spacing-4) 0;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
}

.user-welcome {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.user-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-red), #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--white);
}

.user-info h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.user-email {
  color: var(--gray-300);
  margin-bottom: var(--spacing-3);
}

.user-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

.stat-item i {
  color: var(--primary-red);
}

.dashboard-actions {
  display: flex;
  gap: var(--spacing-3);
}

.dashboard-tabs {
  background-color: var(--gray-800);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.tab-list {
  display: flex;
  background-color: var(--gray-700);
  overflow-x: auto;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-6);
  background: none;
  border: none;
  color: var(--gray-300);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.tab-button:hover {
  background-color: var(--gray-600);
  color: var(--white);
}

.tab-button.active {
  background-color: var(--primary-red);
  color: var(--white);
}

.tab-content {
  padding: var(--spacing-6);
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
}

.empty-icon {
  font-size: 4rem;
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.empty-state p {
  color: var(--gray-400);
  margin-bottom: var(--spacing-6);
}

.bookings-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.bookings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bookings-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
}

.bookings-grid {
  display: grid;
  gap: var(--spacing-4);
}

.booking-card {
  background-color: var(--gray-700);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  border: 1px solid var(--gray-600);
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--gray-600);
}

.booking-code strong {
  color: var(--primary-red);
  font-family: monospace;
  font-size: var(--font-size-lg);
}

.booking-status {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-success {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.status-warning {
  background-color: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.status-error {
  background-color: rgba(220, 38, 38, 0.2);
  color: var(--primary-red);
}

.booking-date {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

.movie-info {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.movie-poster-small {
  width: 60px;
  height: 90px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.movie-details h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-2);
}

.showtime-info,
.cinema-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-300);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}

.showtime-info i,
.cinema-info i {
  color: var(--primary-red);
  width: 14px;
}

.booking-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.seats-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.seats-info strong {
  color: var(--white);
}

.price-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.price-info strong {
  color: var(--white);
}

.price {
  color: var(--primary-red);
  font-weight: var(--font-weight-bold);
}

.booking-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-content {
    grid-template-columns: 1fr;
  }

  .booking-sidebar {
    position: static;
    order: -1;
  }

  .booking-header {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .payment-methods {
    grid-template-columns: 1fr;
  }

  .showtime-details,
  .customer-details {
    grid-template-columns: 1fr;
  }

  .action-buttons,
  .navigation-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .movie-section {
    flex-direction: column;
    text-align: center;
  }

  .movie-poster {
    width: 150px;
    height: 225px;
    margin: 0 auto;
  }

  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .user-stats {
    justify-content: center;
  }

  .dashboard-actions {
    justify-content: center;
  }

  .tab-list {
    justify-content: center;
  }

  .booking-details {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }

  .booking-actions {
    justify-content: center;
  }
}
