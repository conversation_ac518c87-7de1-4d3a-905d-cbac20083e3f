// ===== MAIN APPLICATION =====

class NaCinemaApp {
    constructor() {
        this.initialized = false;
        this.components = {};
        this.pages = {};
    }

    // Initialize application
    async init() {
        if (this.initialized) return;

        try {
            // Show loading
            this.showInitialLoading();

            // Initialize core systems
            await this.initializeCore();

            // Initialize components
            await this.initializeComponents();

            // Initialize pages
            await this.initializePages();

            // Setup global event listeners
            this.setupGlobalEvents();

            // Hide loading
            this.hideInitialLoading();

            this.initialized = true;
            console.log('NaCinema app initialized successfully');

        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showInitializationError(error);
        }
    }

    // Initialize core systems
    async initializeCore() {
        // Initialize authentication
        if (window.auth) {
            // Auth is already initialized in auth.js
            console.log('Authentication system ready');
        }

        // Initialize router
        if (window.router) {
            console.log('Router system ready');
        }

        // Initialize API client
        if (window.api) {
            console.log('API client ready');
        }

        // Test API connection
        try {
            await this.testAPIConnection();
        } catch (error) {
            console.warn('API connection test failed:', error);
        }
    }

    // Test API connection
    async testAPIConnection() {
        try {
            // Try to fetch movies to test API
            await movieAPI.getMovies({ limit: 1 });
            console.log('API connection successful');
        } catch (error) {
            console.warn('API connection failed:', error);
            // Show offline message or fallback
            this.showOfflineMessage();
        }
    }

    // Initialize components
    async initializeComponents() {
        // Navigation is initialized in navigation.js
        
        // Initialize other components as they're created
        this.components = {
            navigation: window.navigation,
            // modal: new Modal(),
            // toast: new Toast(),
            // seatMap: SeatMap
        };

        console.log('Components initialized');
    }

    // Initialize pages
    async initializePages() {
        // Create pages namespace
        window.pages = {
            home: {
                async render() {
                    const mainContent = utils.$('#main-content');
                    mainContent.innerHTML = `
                        <div class="container">
                            <div class="hero-section">
                                <div class="hero-background"></div>
                                <div class="hero-content">
                                    <h1 class="hero-title">
                                        Chào mừng đến với <span class="highlight">NaCinema</span>
                                    </h1>
                                    <p class="hero-description">
                                        Đặt vé xem phim online nhanh chóng, tiện lợi. 
                                        Hàng nghìn suất chiếu, đa dạng thể loại phim mới nhất.
                                    </p>
                                    <div class="hero-actions">
                                        <button class="btn btn-primary btn-lg">
                                            <i class="fas fa-play"></i>
                                            Xem phim ngay
                                        </button>
                                        <button class="btn btn-secondary btn-lg">
                                            <i class="fas fa-info-circle"></i>
                                            Tìm hiểu thêm
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="filters-section">
                                <div class="container">
                                    <div class="filters-container">
                                        <div class="filter-group">
                                            <label class="filter-label">Trạng thái:</label>
                                            <select class="form-select filter-select" id="status-filter">
                                                <option value="all">Tất cả</option>
                                                <option value="active">Đang chiếu</option>
                                                <option value="coming-soon">Sắp chiếu</option>
                                            </select>
                                        </div>
                                        
                                        <div class="filter-group">
                                            <label class="filter-label">Thể loại:</label>
                                            <select class="form-select filter-select" id="genre-filter">
                                                <option value="all">Tất cả</option>
                                                <option value="action">Hành động</option>
                                                <option value="comedy">Hài</option>
                                                <option value="drama">Chính kịch</option>
                                                <option value="horror">Kinh dị</option>
                                                <option value="romance">Lãng mạn</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <section class="movies-section">
                                <div class="section-header">
                                    <h2 class="section-title">Phim đang chiếu</h2>
                                    <div class="section-actions">
                                        <button class="btn btn-secondary" id="grid-view">
                                            <i class="fas fa-th"></i>
                                        </button>
                                        <button class="btn btn-secondary" id="list-view">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="movies-grid" class="movie-grid">
                                    <!-- Movies will be loaded here -->
                                </div>
                                
                                <div id="movies-loading" class="loading-container" style="display: none;">
                                    <div class="spinner"></div>
                                    <p>Đang tải phim...</p>
                                </div>
                            </section>
                        </div>
                    `;

                    // Load movies
                    await this.loadMovies();
                    
                    // Setup filters
                    this.setupFilters();
                },

                async loadMovies(filters = {}) {
                    const moviesGrid = utils.$('#movies-grid');
                    const loadingContainer = utils.$('#movies-loading');
                    
                    try {
                        // Show loading
                        loadingContainer.style.display = 'flex';
                        
                        // Fetch movies
                        const movies = await movieAPI.getMovies(filters);
                        
                        // Render movies
                        this.renderMovies(movies);
                        
                    } catch (error) {
                        console.error('Failed to load movies:', error);
                        moviesGrid.innerHTML = `
                            <div class="error-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Không thể tải danh sách phim. Vui lòng thử lại.</p>
                                <button class="btn btn-primary" onclick="window.pages.home.loadMovies()">
                                    Thử lại
                                </button>
                            </div>
                        `;
                    } finally {
                        loadingContainer.style.display = 'none';
                    }
                },

                renderMovies(movies) {
                    const moviesGrid = utils.$('#movies-grid');
                    
                    if (!movies || movies.length === 0) {
                        moviesGrid.innerHTML = `
                            <div class="empty-state">
                                <i class="fas fa-film"></i>
                                <h3>Không có phim nào</h3>
                                <p>Hiện tại không có phim nào phù hợp với bộ lọc của bạn.</p>
                            </div>
                        `;
                        return;
                    }

                    const moviesHTML = movies.map(movie => `
                        <div class="movie-card" data-movie-id="${movie.id}">
                            <div class="movie-poster">
                                <img src="${movie.poster || '/assets/placeholder-movie.jpg'}" 
                                     alt="${movie.title}" 
                                     loading="lazy">
                                <div class="movie-play-overlay">
                                    <div class="play-button">
                                        <i class="fas fa-play"></i>
                                    </div>
                                </div>
                                ${movie.status !== 'active' ? `
                                    <div class="movie-status-badge">
                                        <span class="badge badge-secondary">${movie.status === 'coming-soon' ? 'Sắp chiếu' : 'Ngừng chiếu'}</span>
                                    </div>
                                ` : ''}
                            </div>
                            
                            <div class="movie-info">
                                <h3 class="movie-title">${movie.title}</h3>
                                <div class="movie-meta">
                                    <div class="movie-rating">
                                        <i class="fas fa-star"></i>
                                        <span>8.5</span>
                                    </div>
                                    <span class="movie-genre">${movie.genre}</span>
                                    <span>${movie.duration} phút</span>
                                </div>
                                <p class="movie-description">
                                    ${utils.truncateText(movie.description, 120)}
                                </p>
                                <div class="movie-actions">
                                    <button class="btn btn-primary btn-sm" 
                                            onclick="window.router.navigate('/movies/${movie.id}')">
                                        ${movie.status === 'active' ? 'Đặt vé' : 'Chi tiết'}
                                    </button>
                                    <button class="btn btn-secondary btn-sm">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    moviesGrid.innerHTML = moviesHTML;

                    // Add click handlers
                    const movieCards = utils.$$('.movie-card');
                    movieCards.forEach(card => {
                        card.addEventListener('click', (e) => {
                            if (!e.target.closest('button')) {
                                const movieId = card.dataset.movieId;
                                window.router.navigate(`/movies/${movieId}`);
                            }
                        });
                    });
                },

                setupFilters() {
                    const statusFilter = utils.$('#status-filter');
                    const genreFilter = utils.$('#genre-filter');

                    const applyFilters = () => {
                        const filters = {
                            status: statusFilter.value !== 'all' ? statusFilter.value : undefined,
                            genre: genreFilter.value !== 'all' ? genreFilter.value : undefined
                        };
                        this.loadMovies(filters);
                    };

                    statusFilter.addEventListener('change', applyFilters);
                    genreFilter.addEventListener('change', applyFilters);
                }
            },

            movieDetail: {
                async render(movieId) {
                    const mainContent = utils.$('#main-content');
                    mainContent.innerHTML = `
                        <div class="container">
                            <div class="movie-detail-loading">
                                <div class="spinner"></div>
                                <p>Đang tải thông tin phim...</p>
                            </div>
                        </div>
                    `;

                    try {
                        const movie = await movieAPI.getMovie(movieId);
                        // Render movie detail page
                        mainContent.innerHTML = `
                            <div class="container">
                                <h1>${movie.title}</h1>
                                <p>Movie detail page - Coming soon!</p>
                                <button class="btn btn-secondary" onclick="window.router.back()">
                                    Quay lại
                                </button>
                            </div>
                        `;
                    } catch (error) {
                        mainContent.innerHTML = `
                            <div class="container">
                                <div class="error-page">
                                    <h2>Không tìm thấy phim</h2>
                                    <p>Phim bạn đang tìm kiếm không tồn tại.</p>
                                    <button class="btn btn-primary" onclick="window.router.navigate('/')">
                                        Về trang chủ
                                    </button>
                                </div>
                            </div>
                        `;
                    }
                }
            },

            login: {
                async render() {
                    const mainContent = utils.$('#main-content');
                    mainContent.innerHTML = `
                        <div class="container">
                            <div class="auth-page">
                                <div class="auth-form">
                                    <h2>Đăng nhập</h2>
                                    <p>Login page - Coming soon!</p>
                                    <button class="btn btn-secondary" onclick="window.router.navigate('/')">
                                        Về trang chủ
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            },

            register: {
                async render() {
                    const mainContent = utils.$('#main-content');
                    mainContent.innerHTML = `
                        <div class="container">
                            <div class="auth-page">
                                <div class="auth-form">
                                    <h2>Đăng ký</h2>
                                    <p>Register page - Coming soon!</p>
                                    <button class="btn btn-secondary" onclick="window.router.navigate('/')">
                                        Về trang chủ
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        };

        console.log('Pages initialized');
    }

    // Setup global event listeners
    setupGlobalEvents() {
        // Handle online/offline status
        window.addEventListener('online', () => {
            this.hideOfflineMessage();
            console.log('App is online');
        });

        window.addEventListener('offline', () => {
            this.showOfflineMessage();
            console.log('App is offline');
        });

        // Handle unhandled errors
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
        });
    }

    // Show initial loading
    showInitialLoading() {
        const spinner = utils.$('#loading-spinner');
        if (spinner) {
            spinner.style.display = 'flex';
        }
    }

    // Hide initial loading
    hideInitialLoading() {
        const spinner = utils.$('#loading-spinner');
        if (spinner) {
            utils.fadeOut(spinner, 300);
        }
    }

    // Show initialization error
    showInitializationError(error) {
        const mainContent = utils.$('#main-content');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="container">
                    <div class="error-page">
                        <div class="error-content">
                            <i class="fas fa-exclamation-triangle error-icon"></i>
                            <h2>Lỗi khởi tạo ứng dụng</h2>
                            <p>Có lỗi xảy ra khi khởi tạo ứng dụng. Vui lòng thử lại.</p>
                            <p class="error-details">${error.message}</p>
                            <button class="btn btn-primary" onclick="window.location.reload()">
                                Tải lại trang
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
        this.hideInitialLoading();
    }

    // Show offline message
    showOfflineMessage() {
        // Implementation for offline message
        console.log('Show offline message');
    }

    // Hide offline message
    hideOfflineMessage() {
        // Implementation for hiding offline message
        console.log('Hide offline message');
    }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    window.app = new NaCinemaApp();
    await window.app.init();
});

// Export to global scope
window.NaCinemaApp = NaCinemaApp;
