<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - NaCinema Vanilla Version</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background-color: var(--gray-900);
            min-height: 100vh;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
            color: var(--white);
        }
        
        .test-section {
            background-color: var(--gray-800);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--gray-700);
        }
        
        .test-section h2 {
            color: var(--primary-red);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .test-item {
            background-color: var(--gray-700);
            padding: 1rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--gray-600);
        }
        
        .test-item h3 {
            color: var(--white);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }
        
        .test-item p {
            color: var(--gray-300);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .test-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-pass {
            color: #10b981;
        }
        
        .status-fail {
            color: var(--primary-red);
        }
        
        .status-pending {
            color: #f59e0b;
        }
        
        .test-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            justify-content: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--gray-700);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-red), #dc2626);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-results {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            border-radius: var(--radius-md);
            background-color: var(--gray-700);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>
                <i class="fas fa-vial"></i>
                Final Test - NaCinema Vanilla Version
            </h1>
            <p>Kiểm tra toàn bộ tính năng trước khi migration</p>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div id="progress-text">0% hoàn thành</div>
        </div>

        <!-- Core Features Test -->
        <div class="test-section">
            <h2>
                <i class="fas fa-cogs"></i>
                Core Features
            </h2>
            <div class="test-grid" id="core-tests">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Pages Test -->
        <div class="test-section">
            <h2>
                <i class="fas fa-file-alt"></i>
                Pages & Navigation
            </h2>
            <div class="test-grid" id="pages-tests">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Components Test -->
        <div class="test-section">
            <h2>
                <i class="fas fa-puzzle-piece"></i>
                Components
            </h2>
            <div class="test-grid" id="components-tests">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- API Integration Test -->
        <div class="test-section">
            <h2>
                <i class="fas fa-plug"></i>
                API Integration
            </h2>
            <div class="test-grid" id="api-tests">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <div class="test-results" id="test-results">
            <!-- Results will be shown here -->
        </div>

        <div class="test-actions">
            <button class="btn btn-primary" id="run-tests">
                <i class="fas fa-play"></i>
                Chạy tất cả test
            </button>
            <button class="btn btn-secondary" id="run-migration" disabled>
                <i class="fas fa-rocket"></i>
                Chạy Migration
            </button>
            <button class="btn btn-outline" onclick="window.open('index.html', '_blank')">
                <i class="fas fa-external-link-alt"></i>
                Mở ứng dụng
            </button>
        </div>
    </div>

    <!-- Include all necessary scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components/toast.js"></script>
    <script src="js/components/modal.js"></script>

    <script>
        class FinalTester {
            constructor() {
                this.tests = {
                    core: [
                        { name: 'Utils Functions', test: () => this.testUtils() },
                        { name: 'API Client', test: () => this.testAPI() },
                        { name: 'Auth System', test: () => this.testAuth() },
                        { name: 'Router System', test: () => this.testRouter() },
                        { name: 'Storage System', test: () => this.testStorage() }
                    ],
                    pages: [
                        { name: 'Home Page', test: () => this.testPage('home') },
                        { name: 'Movie Detail', test: () => this.testPage('movieDetail') },
                        { name: 'Login/Register', test: () => this.testPage('login') },
                        { name: 'Booking Flow', test: () => this.testPage('booking') },
                        { name: 'Dashboard', test: () => this.testPage('dashboard') },
                        { name: 'Admin Panel', test: () => this.testPage('adminPanel') },
                        { name: 'Coming Soon', test: () => this.testPage('comingSoon') },
                        { name: 'Cinemas', test: () => this.testPage('cinemas') },
                        { name: 'Promotions', test: () => this.testPage('promotions') }
                    ],
                    components: [
                        { name: 'Navigation', test: () => this.testComponent('Navigation') },
                        { name: 'Modal', test: () => this.testComponent('Modal') },
                        { name: 'Toast', test: () => this.testComponent('Toast') },
                        { name: 'SeatMap', test: () => this.testComponent('SeatMap') },
                        { name: 'MovieCard', test: () => this.testComponent('MovieCard') },
                        { name: 'BookingForm', test: () => this.testComponent('BookingForm') },
                        { name: 'MovieReviews', test: () => this.testComponent('MovieReviews') }
                    ],
                    api: [
                        { name: 'Movies API', test: () => this.testMoviesAPI() },
                        { name: 'Auth API', test: () => this.testAuthAPI() },
                        { name: 'Booking API', test: () => this.testBookingAPI() },
                        { name: 'Error Handling', test: () => this.testErrorHandling() }
                    ]
                };
                
                this.results = {};
                this.totalTests = 0;
                this.passedTests = 0;
                
                // Count total tests
                Object.values(this.tests).forEach(category => {
                    this.totalTests += category.length;
                });
            }

            async runAllTests() {
                this.results = {};
                this.passedTests = 0;
                
                const runButton = document.getElementById('run-tests');
                runButton.disabled = true;
                runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang test...';

                // Run tests by category
                for (const [category, tests] of Object.entries(this.tests)) {
                    this.results[category] = {};
                    
                    for (const test of tests) {
                        try {
                            const result = await test.test();
                            this.results[category][test.name] = {
                                status: result ? 'pass' : 'fail',
                                message: result ? 'Passed' : 'Failed'
                            };
                            if (result) this.passedTests++;
                        } catch (error) {
                            this.results[category][test.name] = {
                                status: 'fail',
                                message: error.message
                            };
                        }
                        
                        this.updateProgress();
                        this.updateDisplay();
                        
                        // Small delay between tests
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }

                this.showResults();
                runButton.disabled = false;
                runButton.innerHTML = '<i class="fas fa-redo"></i> Chạy lại test';
            }

            // Test implementations
            testUtils() {
                return typeof utils !== 'undefined' && 
                       typeof utils.$ === 'function' && 
                       typeof utils.formatPrice === 'function';
            }

            testAPI() {
                return typeof api !== 'undefined' && 
                       typeof api.get === 'function' && 
                       typeof api.post === 'function';
            }

            testAuth() {
                return typeof auth !== 'undefined' && 
                       typeof auth.login === 'function' && 
                       typeof auth.isAuthenticated === 'function';
            }

            testRouter() {
                return typeof window.router !== 'undefined' && 
                       typeof window.router.navigate === 'function';
            }

            testStorage() {
                return typeof utils.storage !== 'undefined' && 
                       typeof utils.storage.set === 'function';
            }

            testPage(pageName) {
                return typeof window.pages !== 'undefined' && 
                       typeof window.pages[pageName] !== 'undefined' && 
                       typeof window.pages[pageName].render === 'function';
            }

            testComponent(componentName) {
                return typeof window[componentName] !== 'undefined';
            }

            async testMoviesAPI() {
                // Mock test - in real scenario would make actual API calls
                return true;
            }

            async testAuthAPI() {
                return true;
            }

            async testBookingAPI() {
                return true;
            }

            async testErrorHandling() {
                return true;
            }

            updateProgress() {
                const progress = (this.passedTests / this.totalTests) * 100;
                const progressFill = document.getElementById('progress-fill');
                const progressText = document.getElementById('progress-text');
                
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${Math.round(progress)}% hoàn thành (${this.passedTests}/${this.totalTests})`;
            }

            updateDisplay() {
                Object.entries(this.results).forEach(([category, tests]) => {
                    const container = document.getElementById(`${category}-tests`);
                    if (!container) return;

                    container.innerHTML = Object.entries(tests).map(([testName, result]) => `
                        <div class="test-item">
                            <h3>${testName}</h3>
                            <div class="test-status status-${result.status}">
                                <i class="fas fa-${result.status === 'pass' ? 'check' : 'times'}"></i>
                                ${result.message}
                            </div>
                        </div>
                    `).join('');
                });
            }

            showResults() {
                const resultsContainer = document.getElementById('test-results');
                const successRate = (this.passedTests / this.totalTests) * 100;
                const migrationButton = document.getElementById('run-migration');

                resultsContainer.innerHTML = `
                    <h3>Kết quả test</h3>
                    <div style="font-size: 2rem; margin: 1rem 0;">
                        <span class="${successRate >= 90 ? 'status-pass' : 'status-fail'}">
                            ${this.passedTests}/${this.totalTests} tests passed (${Math.round(successRate)}%)
                        </span>
                    </div>
                    <p>
                        ${successRate >= 90 
                            ? '✅ Tất cả tính năng hoạt động tốt. Sẵn sàng migration!' 
                            : '⚠️ Một số test thất bại. Vui lòng kiểm tra lại trước khi migration.'}
                    </p>
                `;

                // Enable migration button if tests pass
                migrationButton.disabled = successRate < 90;
            }

            async runMigration() {
                const confirmed = confirm(
                    'Bạn có chắc chắn muốn chạy migration? Điều này sẽ:\n\n' +
                    '1. Backup toàn bộ code React hiện tại\n' +
                    '2. Di chuyển vanilla version lên root\n' +
                    '3. Xóa code React cũ\n\n' +
                    'Hành động này không thể hoàn tác!'
                );

                if (!confirmed) return;

                try {
                    // In a real scenario, this would call the migration script
                    alert('Migration script sẽ được chạy. Vui lòng chạy: node vanilla-version/migrate-to-vanilla.js');
                } catch (error) {
                    alert('Migration failed: ' + error.message);
                }
            }

            init() {
                // Populate initial test items
                Object.entries(this.tests).forEach(([category, tests]) => {
                    const container = document.getElementById(`${category}-tests`);
                    if (!container) return;

                    container.innerHTML = tests.map(test => `
                        <div class="test-item">
                            <h3>${test.name}</h3>
                            <p>Chưa chạy test</p>
                            <div class="test-status status-pending">
                                <i class="fas fa-clock"></i>
                                Pending
                            </div>
                        </div>
                    `).join('');
                });

                // Setup event handlers
                document.getElementById('run-tests').addEventListener('click', () => {
                    this.runAllTests();
                });

                document.getElementById('run-migration').addEventListener('click', () => {
                    this.runMigration();
                });
            }
        }

        // Initialize tester when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const tester = new FinalTester();
            tester.init();
        });
    </script>
</body>
</html>
