// ===== USER DASHBOARD PAGE =====

window.pages.dashboard = {
    currentUser: null,
    bookings: [],
    currentTab: 'bookings',

    async render() {
        const mainContent = utils.$('#main-content');
        
        try {
            // Check authentication
            if (!auth.isAuthenticated()) {
                window.router.navigate('/login');
                return;
            }

            this.currentUser = auth.getCurrentUser();
            
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="dashboard-loading">
                        <div class="spinner"></div>
                        <p>Đang tải thông tin...</p>
                    </div>
                </div>
            `;

            // Load user data
            await this.loadUserData();
            
            // Render dashboard
            this.renderDashboard();
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to render dashboard:', error);
            this.renderError('Có lỗi x<PERSON>y ra khi tải dashboard.');
        }
    },

    async loadUserData() {
        try {
            // Load user bookings
            this.bookings = await api.get('/api/bookings/my-bookings');
            
            // Load user profile
            const profile = await api.get('/api/auth/profile');
            this.currentUser = { ...this.currentUser, ...profile };
            
        } catch (error) {
            console.error('Failed to load user data:', error);
            this.bookings = [];
        }
    },

    renderDashboard() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="dashboard-page">
                <div class="container">
                    <!-- Dashboard Header -->
                    <div class="dashboard-header">
                        <div class="user-welcome">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info">
                                <h1>Xin chào, ${this.currentUser.fullName || this.currentUser.username}!</h1>
                                <p class="user-email">${this.currentUser.email}</p>
                                <div class="user-stats">
                                    <span class="stat-item">
                                        <i class="fas fa-ticket-alt"></i>
                                        ${this.bookings.length} vé đã đặt
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-star"></i>
                                        Thành viên từ ${utils.formatDate(this.currentUser.createdAt)}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-actions">
                            <button class="btn btn-primary" id="book-ticket-btn">
                                <i class="fas fa-plus"></i>
                                Đặt vé mới
                            </button>
                            <button class="btn btn-secondary" id="edit-profile-btn">
                                <i class="fas fa-edit"></i>
                                Sửa thông tin
                            </button>
                        </div>
                    </div>

                    <!-- Dashboard Tabs -->
                    <div class="dashboard-tabs">
                        <div class="tab-list">
                            <button class="tab-button ${this.currentTab === 'bookings' ? 'active' : ''}" data-tab="bookings">
                                <i class="fas fa-ticket-alt"></i>
                                Vé của tôi
                            </button>
                            <button class="tab-button ${this.currentTab === 'profile' ? 'active' : ''}" data-tab="profile">
                                <i class="fas fa-user"></i>
                                Thông tin cá nhân
                            </button>
                            <button class="tab-button ${this.currentTab === 'favorites' ? 'active' : ''}" data-tab="favorites">
                                <i class="fas fa-heart"></i>
                                Phim yêu thích
                            </button>
                            <button class="tab-button ${this.currentTab === 'reviews' ? 'active' : ''}" data-tab="reviews">
                                <i class="fas fa-star"></i>
                                Đánh giá của tôi
                            </button>
                        </div>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <div class="tab-pane ${this.currentTab === 'bookings' ? 'active' : ''}" id="bookings-tab">
                                ${this.renderBookingsTab()}
                            </div>
                            <div class="tab-pane ${this.currentTab === 'profile' ? 'active' : ''}" id="profile-tab">
                                ${this.renderProfileTab()}
                            </div>
                            <div class="tab-pane ${this.currentTab === 'favorites' ? 'active' : ''}" id="favorites-tab">
                                ${this.renderFavoritesTab()}
                            </div>
                            <div class="tab-pane ${this.currentTab === 'reviews' ? 'active' : ''}" id="reviews-tab">
                                ${this.renderReviewsTab()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderBookingsTab() {
        if (this.bookings.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3>Chưa có vé nào</h3>
                    <p>Bạn chưa đặt vé xem phim nào. Hãy khám phá các bộ phim đang chiếu!</p>
                    <button class="btn btn-primary" onclick="window.router.navigate('/')">
                        <i class="fas fa-film"></i>
                        Xem phim ngay
                    </button>
                </div>
            `;
        }

        return `
            <div class="bookings-list">
                <div class="bookings-header">
                    <h3>Lịch sử đặt vé</h3>
                    <div class="booking-filters">
                        <select class="form-select" id="booking-status-filter">
                            <option value="all">Tất cả trạng thái</option>
                            <option value="confirmed">Đã xác nhận</option>
                            <option value="pending">Chờ thanh toán</option>
                            <option value="cancelled">Đã hủy</option>
                        </select>
                    </div>
                </div>
                
                <div class="bookings-grid">
                    ${this.bookings.map(booking => this.renderBookingCard(booking)).join('')}
                </div>
            </div>
        `;
    },

    renderBookingCard(booking) {
        const statusClass = {
            'confirmed': 'success',
            'pending': 'warning', 
            'cancelled': 'error'
        }[booking.status] || 'info';

        return `
            <div class="booking-card">
                <div class="booking-header">
                    <div class="booking-code">
                        <strong>#${booking.code}</strong>
                        <span class="booking-status status-${statusClass}">
                            ${this.getStatusText(booking.status)}
                        </span>
                    </div>
                    <div class="booking-date">
                        ${utils.formatDate(booking.createdAt)}
                    </div>
                </div>
                
                <div class="booking-content">
                    <div class="movie-info">
                        <img src="${booking.movie.poster}" alt="${booking.movie.title}" class="movie-poster-small">
                        <div class="movie-details">
                            <h4>${booking.movie.title}</h4>
                            <p class="showtime-info">
                                <i class="fas fa-calendar"></i>
                                ${utils.formatDate(booking.showtime.date)} - ${utils.formatTime(booking.showtime.startTime)}
                            </p>
                            <p class="cinema-info">
                                <i class="fas fa-map-marker-alt"></i>
                                ${booking.cinema.name} - Phòng ${booking.room.name}
                            </p>
                        </div>
                    </div>
                    
                    <div class="booking-details">
                        <div class="seats-info">
                            <strong>Ghế:</strong>
                            ${booking.seats.map(seat => `<span class="seat-badge">${seat.seatNumber}</span>`).join('')}
                        </div>
                        <div class="price-info">
                            <strong>Tổng tiền:</strong>
                            <span class="price">${utils.formatPrice(booking.totalAmount)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="booking-actions">
                    ${booking.status === 'confirmed' ? `
                        <button class="btn btn-sm btn-primary" onclick="window.pages.dashboard.viewTicket('${booking.code}')">
                            <i class="fas fa-eye"></i>
                            Xem vé
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="window.pages.dashboard.downloadTicket('${booking.code}')">
                            <i class="fas fa-download"></i>
                            Tải vé
                        </button>
                    ` : ''}
                    
                    ${booking.status === 'pending' ? `
                        <button class="btn btn-sm btn-primary" onclick="window.pages.dashboard.continuePayment('${booking.code}')">
                            <i class="fas fa-credit-card"></i>
                            Thanh toán
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="window.pages.dashboard.cancelBooking('${booking.code}')">
                            <i class="fas fa-times"></i>
                            Hủy đặt
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    },

    renderProfileTab() {
        return `
            <div class="profile-section">
                <div class="profile-header">
                    <h3>Thông tin cá nhân</h3>
                    <button class="btn btn-secondary" id="edit-profile-toggle">
                        <i class="fas fa-edit"></i>
                        Chỉnh sửa
                    </button>
                </div>
                
                <form class="profile-form" id="profile-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Họ và tên</label>
                            <input type="text" name="fullName" class="form-input" value="${this.currentUser.fullName || ''}" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <input type="email" name="email" class="form-input" value="${this.currentUser.email || ''}" readonly>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Số điện thoại</label>
                            <input type="tel" name="phone" class="form-input" value="${this.currentUser.phone || ''}" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Ngày sinh</label>
                            <input type="date" name="dateOfBirth" class="form-input" value="${this.currentUser.dateOfBirth || ''}" readonly>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Địa chỉ</label>
                        <textarea name="address" class="form-input" rows="3" readonly>${this.currentUser.address || ''}</textarea>
                    </div>
                    
                    <div class="form-actions" style="display: none;">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Lưu thay đổi
                        </button>
                        <button type="button" class="btn btn-secondary" id="cancel-edit">
                            <i class="fas fa-times"></i>
                            Hủy
                        </button>
                    </div>
                </form>
                
                <div class="password-section">
                    <h4>Đổi mật khẩu</h4>
                    <form class="password-form" id="password-form">
                        <div class="form-group">
                            <label class="form-label">Mật khẩu hiện tại</label>
                            <input type="password" name="currentPassword" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Mật khẩu mới</label>
                            <input type="password" name="newPassword" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Xác nhận mật khẩu mới</label>
                            <input type="password" name="confirmPassword" class="form-input" required>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key"></i>
                            Đổi mật khẩu
                        </button>
                    </form>
                </div>
            </div>
        `;
    },

    renderFavoritesTab() {
        return `
            <div class="favorites-section">
                <div class="favorites-header">
                    <h3>Phim yêu thích</h3>
                    <p>Danh sách các bộ phim bạn đã đánh dấu yêu thích</p>
                </div>
                
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h4>Chưa có phim yêu thích</h4>
                    <p>Hãy khám phá và đánh dấu những bộ phim bạn yêu thích!</p>
                    <button class="btn btn-primary" onclick="window.router.navigate('/')">
                        <i class="fas fa-film"></i>
                        Khám phá phim
                    </button>
                </div>
            </div>
        `;
    },

    renderReviewsTab() {
        return `
            <div class="reviews-section">
                <div class="reviews-header">
                    <h3>Đánh giá của tôi</h3>
                    <p>Các đánh giá và nhận xét bạn đã viết về phim</p>
                </div>
                
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h4>Chưa có đánh giá nào</h4>
                    <p>Hãy xem phim và chia sẻ cảm nhận của bạn!</p>
                    <button class="btn btn-primary" onclick="window.router.navigate('/')">
                        <i class="fas fa-film"></i>
                        Xem phim ngay
                    </button>
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Tab switching
        const tabButtons = utils.$$('.tab-button');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const tab = e.target.closest('.tab-button').dataset.tab;
                this.switchTab(tab);
            });
        });

        // Book ticket button
        const bookTicketBtn = utils.$('#book-ticket-btn');
        if (bookTicketBtn) {
            bookTicketBtn.addEventListener('click', () => {
                window.router.navigate('/');
            });
        }

        // Edit profile button
        const editProfileBtn = utils.$('#edit-profile-btn');
        if (editProfileBtn) {
            editProfileBtn.addEventListener('click', () => {
                this.switchTab('profile');
                this.enableProfileEdit();
            });
        }

        // Profile form handlers
        this.setupProfileHandlers();
    },

    setupProfileHandlers() {
        const editToggle = utils.$('#edit-profile-toggle');
        const profileForm = utils.$('#profile-form');
        const passwordForm = utils.$('#password-form');

        if (editToggle) {
            editToggle.addEventListener('click', () => {
                this.enableProfileEdit();
            });
        }

        if (profileForm) {
            profileForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateProfile();
            });
        }

        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.changePassword();
            });
        }
    },

    switchTab(tab) {
        this.currentTab = tab;
        
        // Update tab buttons
        utils.$$('.tab-button').forEach(btn => btn.classList.remove('active'));
        utils.$(`[data-tab="${tab}"]`).classList.add('active');
        
        // Update tab content
        utils.$$('.tab-pane').forEach(pane => pane.classList.remove('active'));
        utils.$(`#${tab}-tab`).classList.add('active');
    },

    enableProfileEdit() {
        const inputs = utils.$$('#profile-form input, #profile-form textarea');
        const actions = utils.$('#profile-form .form-actions');
        
        inputs.forEach(input => input.removeAttribute('readonly'));
        actions.style.display = 'block';
    },

    async updateProfile() {
        const form = utils.$('#profile-form');
        const formData = new FormData(form);
        
        try {
            const updatedUser = await api.put('/api/auth/profile', {
                fullName: formData.get('fullName'),
                phone: formData.get('phone'),
                dateOfBirth: formData.get('dateOfBirth'),
                address: formData.get('address')
            });
            
            this.currentUser = { ...this.currentUser, ...updatedUser };
            auth.setCurrentUser(this.currentUser);
            
            Toast.success('Cập nhật thông tin thành công!');
            this.disableProfileEdit();
            
        } catch (error) {
            Toast.error('Không thể cập nhật thông tin. Vui lòng thử lại.');
        }
    },

    disableProfileEdit() {
        const inputs = utils.$$('#profile-form input, #profile-form textarea');
        const actions = utils.$('#profile-form .form-actions');
        
        inputs.forEach(input => input.setAttribute('readonly', true));
        actions.style.display = 'none';
    },

    async changePassword() {
        const form = utils.$('#password-form');
        const formData = new FormData(form);
        
        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');
        
        if (newPassword !== confirmPassword) {
            Toast.error('Mật khẩu xác nhận không khớp!');
            return;
        }
        
        try {
            await api.put('/api/auth/change-password', {
                currentPassword,
                newPassword
            });
            
            Toast.success('Đổi mật khẩu thành công!');
            form.reset();
            
        } catch (error) {
            Toast.error('Không thể đổi mật khẩu. Vui lòng kiểm tra lại.');
        }
    },

    getStatusText(status) {
        const statusTexts = {
            'confirmed': 'Đã xác nhận',
            'pending': 'Chờ thanh toán',
            'cancelled': 'Đã hủy'
        };
        return statusTexts[status] || status;
    },

    async viewTicket(bookingCode) {
        try {
            const booking = await api.get(`/api/bookings/${bookingCode}`);
            window.router.navigate(`/booking/success/${bookingCode}`);
        } catch (error) {
            Toast.error('Không thể xem vé. Vui lòng thử lại.');
        }
    },

    async downloadTicket(bookingCode) {
        try {
            Toast.info('Đang tạo file PDF...');
            // Implementation for PDF download
            setTimeout(() => {
                Toast.success('Tải vé thành công!');
            }, 2000);
        } catch (error) {
            Toast.error('Không thể tải vé. Vui lòng thử lại.');
        }
    },

    async continuePayment(bookingCode) {
        try {
            const booking = await api.get(`/api/bookings/${bookingCode}`);
            window.router.navigate(`/booking/${booking.showtimeId}?continue=${bookingCode}`);
        } catch (error) {
            Toast.error('Không thể tiếp tục thanh toán. Vui lòng thử lại.');
        }
    },

    async cancelBooking(bookingCode) {
        const confirmed = await Modal.confirm(
            'Bạn có chắc chắn muốn hủy đặt vé này?',
            { title: 'Xác nhận hủy vé' }
        );
        
        if (confirmed) {
            try {
                await api.delete(`/api/bookings/${bookingCode}`);
                Toast.success('Hủy đặt vé thành công!');
                
                // Reload bookings
                await this.loadUserData();
                this.renderDashboard();
                this.setupEventHandlers();
                
            } catch (error) {
                Toast.error('Không thể hủy đặt vé. Vui lòng thử lại.');
            }
        }
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải dashboard</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.pages.dashboard.render()">
                                Thử lại
                            </button>
                            <a href="#" class="btn btn-secondary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
