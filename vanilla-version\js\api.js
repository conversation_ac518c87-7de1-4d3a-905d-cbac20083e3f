// ===== API CLIENT =====

class APIClient {
  constructor(baseURL = "/api") {
    this.baseURL = baseURL;
    this.cache = new Map();
    this.pendingRequests = new Map();
  }

  // Get auth token from localStorage
  getAuthToken() {
    return utils.storage.get("authToken");
  }

  // Set default headers
  getHeaders(customHeaders = {}) {
    const headers = {
      "Content-Type": "application/json",
      ...customHeaders,
    };

    const token = this.getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  // Generate cache key
  getCacheKey(url, options = {}) {
    return `${url}_${JSON.stringify(options)}`;
  }

  // Check if request is cacheable
  isCacheable(method) {
    return method === "GET";
  }

  // Make HTTP request
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const method = options.method || "GET";
    const cacheKey = this.getCacheKey(url, options);

    // Return cached response for GET requests
    if (this.isCacheable(method) && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < (options.cacheTime || 300000)) {
        // 5 minutes default
        return cached.data;
      }
    }

    // Prevent duplicate requests
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }

    const requestOptions = {
      method,
      headers: this.getHeaders(options.headers),
      ...options,
    };

    if (options.body && method !== "GET") {
      requestOptions.body = JSON.stringify(options.body);
    }

    const requestPromise = fetch(url, requestOptions)
      .then(async response => {
        this.pendingRequests.delete(cacheKey);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new APIError(
            errorData.message || `HTTP ${response.status}`,
            response.status,
            errorData
          );
        }

        const data = await response.json();

        // Cache successful GET requests
        if (this.isCacheable(method)) {
          this.cache.set(cacheKey, {
            data,
            timestamp: Date.now(),
          });
        }

        return data;
      })
      .catch(error => {
        this.pendingRequests.delete(cacheKey);
        throw error;
      });

    this.pendingRequests.set(cacheKey, requestPromise);
    return requestPromise;
  }

  // HTTP Methods
  async get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: "GET" });
  }

  async post(endpoint, body, options = {}) {
    return this.request(endpoint, { ...options, method: "POST", body });
  }

  async put(endpoint, body, options = {}) {
    return this.request(endpoint, { ...options, method: "PUT", body });
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: "DELETE" });
  }

  // Clear cache
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // Invalidate specific cache entries
  invalidateCache(patterns) {
    if (typeof patterns === "string") {
      patterns = [patterns];
    }

    patterns.forEach(pattern => {
      this.clearCache(pattern);
    });
  }
}

// Custom API Error class
class APIError extends Error {
  constructor(message, status, data = {}) {
    super(message);
    this.name = "APIError";
    this.status = status;
    this.data = data;
  }
}

// Create global API instance
const api = new APIClient();

// ===== API ENDPOINTS =====

const movieAPI = {
  // Get all movies
  getMovies: (filters = {}) => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== "all") {
        params.append(key, value);
      }
    });

    const query = params.toString();
    return api.get(`/movies${query ? `?${query}` : ""}`);
  },

  // Get movie by ID
  getMovie: id => api.get(`/movies/${id}`),

  // Get movie showtimes
  getMovieShowtimes: (id, date = null) => {
    const params = date ? `?date=${date}` : "";
    return api.get(`/movies/${id}/showtimes${params}`);
  },

  // Get movie reviews
  getMovieReviews: id => api.get(`/movies/${id}/reviews`),

  // Add movie review
  addMovieReview: (id, review) => api.post(`/movies/${id}/reviews`, review),

  // Admin: Create movie
  createMovie: movieData => api.post("/movies", movieData),

  // Admin: Update movie
  updateMovie: (id, movieData) => api.put(`/movies/${id}`, movieData),

  // Admin: Delete movie
  deleteMovie: id => api.delete(`/movies/${id}`),
};

const showtimeAPI = {
  // Get showtimes
  getShowtimes: (filters = {}) => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value);
    });

    const query = params.toString();
    return api.get(`/showtimes${query ? `?${query}` : ""}`);
  },

  // Get showtime by ID
  getShowtime: id => api.get(`/showtimes/${id}`),

  // Get seat map for showtime
  getSeatMap: id => api.get(`/showtimes/${id}/seats`),

  // Admin: Create showtime
  createShowtime: showtimeData => api.post("/showtimes", showtimeData),

  // Admin: Update showtime
  updateShowtime: (id, showtimeData) =>
    api.put(`/showtimes/${id}`, showtimeData),

  // Admin: Delete showtime
  deleteShowtime: id => api.delete(`/showtimes/${id}`),
};

const bookingAPI = {
  // Create booking
  createBooking: bookingData => api.post("/bookings", bookingData),

  // Get user bookings
  getUserBookings: () => api.get("/tickets"),

  // Get booking by code
  getBookingByCode: code => api.get(`/tickets/${code}`),

  // Cancel booking
  cancelBooking: id => api.put(`/bookings/${id}/cancel`),

  // Admin: Get all bookings
  getAllBookings: (filters = {}) => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value);
    });

    const query = params.toString();
    return api.get(`/admin/all-tickets${query ? `?${query}` : ""}`);
  },

  // Admin: Update booking status
  updateBookingStatus: (id, status) =>
    api.put(`/admin/tickets/${id}/status`, { status }),
};

const authAPI = {
  // Login
  login: credentials => api.post("/auth/login", credentials),

  // Register
  register: userData => api.post("/auth/register", userData),

  // Logout
  logout: () => api.post("/auth/logout"),

  // Get current user
  getCurrentUser: () => api.get("/auth/me"),

  // Refresh token
  refreshToken: () => api.post("/auth/refresh"),
};

const cinemaAPI = {
  // Get all cinemas
  getCinemas: () => api.get("/cinemas"),

  // Get cinema by ID
  getCinema: id => api.get(`/cinemas/${id}`),

  // Get cinema rooms
  getCinemaRooms: id => api.get(`/cinemas/${id}/rooms`),
};

const promotionAPI = {
  // Get active promotions
  getActivePromotions: () => api.get("/promotions/active"),

  // Validate promotion code
  validatePromotion: (code, totalAmount) =>
    api.post("/promotions/validate", { code, totalAmount }),

  // Admin: Get all promotions
  getAllPromotions: () => api.get("/promotions"),

  // Admin: Create promotion
  createPromotion: promotionData => api.post("/promotions", promotionData),

  // Admin: Update promotion
  updatePromotion: (id, promotionData) =>
    api.put(`/promotions/${id}`, promotionData),

  // Admin: Delete promotion
  deletePromotion: id => api.delete(`/promotions/${id}`),
};

const adminAPI = {
  // Get dashboard stats
  getDashboard: () => api.get("/admin/dashboard"),

  // Get all users
  getUsers: () => api.get("/admin/users"),

  // Update user role
  updateUserRole: (id, role) => api.put(`/admin/users/${id}/role`, { role }),

  // Get analytics
  getAnalytics: dateRange =>
    api.get(`/admin/analytics?${new URLSearchParams(dateRange)}`),

  // Get revenue report
  getRevenueReport: dateRange =>
    api.get(`/admin/revenue?${new URLSearchParams(dateRange)}`),
};

// ===== STATE MANAGEMENT =====

class StateManager {
  constructor() {
    this.state = {};
    this.listeners = {};
  }

  // Set state
  setState(key, value) {
    const oldValue = this.state[key];
    this.state[key] = value;

    if (this.listeners[key]) {
      this.listeners[key].forEach(callback => {
        callback(value, oldValue);
      });
    }
  }

  // Get state
  getState(key) {
    return this.state[key];
  }

  // Subscribe to state changes
  subscribe(key, callback) {
    if (!this.listeners[key]) {
      this.listeners[key] = [];
    }
    this.listeners[key].push(callback);

    // Return unsubscribe function
    return () => {
      this.listeners[key] = this.listeners[key].filter(cb => cb !== callback);
    };
  }

  // Clear state
  clearState(key = null) {
    if (key) {
      delete this.state[key];
    } else {
      this.state = {};
    }
  }
}

// Create global state manager
const state = new StateManager();

// ===== QUERY CACHE MANAGER =====

class QueryCache {
  constructor() {
    this.queries = new Map();
    this.subscribers = new Map();
  }

  // Execute query with caching
  async query(key, queryFn, options = {}) {
    const {
      cacheTime = 300000, // 5 minutes
      staleTime = 60000, // 1 minute
      refetchOnWindowFocus = true,
    } = options;

    // Check if we have cached data
    if (this.queries.has(key)) {
      const cached = this.queries.get(key);
      const now = Date.now();

      // Return cached data if still fresh
      if (now - cached.timestamp < staleTime) {
        return cached.data;
      }

      // Return stale data but refetch in background
      if (now - cached.timestamp < cacheTime) {
        this.backgroundRefetch(key, queryFn);
        return cached.data;
      }
    }

    // Fetch fresh data
    try {
      const data = await queryFn();
      this.setQueryData(key, data);
      return data;
    } catch (error) {
      // Return stale data if available and fetch failed
      if (this.queries.has(key)) {
        return this.queries.get(key).data;
      }
      throw error;
    }
  }

  // Set query data
  setQueryData(key, data) {
    this.queries.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Notify subscribers
    if (this.subscribers.has(key)) {
      this.subscribers.get(key).forEach(callback => callback(data));
    }
  }

  // Subscribe to query updates
  subscribe(key, callback) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    this.subscribers.get(key).add(callback);

    return () => {
      const subs = this.subscribers.get(key);
      if (subs) {
        subs.delete(callback);
        if (subs.size === 0) {
          this.subscribers.delete(key);
        }
      }
    };
  }

  // Background refetch
  async backgroundRefetch(key, queryFn) {
    try {
      const data = await queryFn();
      this.setQueryData(key, data);
    } catch (error) {
      console.warn("Background refetch failed:", error);
    }
  }

  // Invalidate queries
  invalidateQueries(pattern) {
    for (const key of this.queries.keys()) {
      if (key.includes(pattern)) {
        this.queries.delete(key);
      }
    }
  }

  // Clear all queries
  clear() {
    this.queries.clear();
    this.subscribers.clear();
  }
}

// Create global query cache
const queryCache = new QueryCache();

// Export to global scope
window.api = api;
window.movieAPI = movieAPI;
window.showtimeAPI = showtimeAPI;
window.bookingAPI = bookingAPI;
window.authAPI = authAPI;
window.cinemaAPI = cinemaAPI;
window.promotionAPI = promotionAPI;
window.adminAPI = adminAPI;
window.state = state;
window.queryCache = queryCache;
window.APIError = APIError;
