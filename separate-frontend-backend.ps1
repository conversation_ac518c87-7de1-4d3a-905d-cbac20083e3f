# NaCinema Frontend/Backend Separation Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   NaCinema Frontend/Backend Separation" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if vanilla-version exists
if (-not (Test-Path "vanilla-version")) {
    Write-Host "ERROR: vanilla-version directory not found!" -ForegroundColor Red
    Write-Host "Please make sure you're in the project root directory." -ForegroundColor Red
    exit 1
}

Write-Host "Step 1: Creating frontend directory structure..." -ForegroundColor Yellow

# Create frontend directories
New-Item -ItemType Directory -Path "frontend" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/css" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/js" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/js/components" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/js/pages" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/assets" -Force | Out-Null

Write-Host "✓ Frontend directories created" -ForegroundColor Green

Write-Host "Step 2: Copying vanilla-version files to frontend..." -ForegroundColor Yellow

# Copy main files
Copy-Item "vanilla-version/index.html" "frontend/" -Force
Copy-Item "vanilla-version/test-features.html" "frontend/" -Force -ErrorAction SilentlyContinue
Copy-Item "vanilla-version/final-test.html" "frontend/" -Force -ErrorAction SilentlyContinue

# Copy CSS files
if (Test-Path "vanilla-version/css") {
    Copy-Item "vanilla-version/css/*" "frontend/css/" -Force -Recurse
}

# Copy JS files
if (Test-Path "vanilla-version/js") {
    Copy-Item "vanilla-version/js/*" "frontend/js/" -Force -Recurse
}

Write-Host "✓ Frontend files copied" -ForegroundColor Green

Write-Host "Step 3: Creating backend directory structure..." -ForegroundColor Yellow

# Create backend directories
New-Item -ItemType Directory -Path "backend" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/routes" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/models" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/middleware" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/config" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/utils" -Force | Out-Null

Write-Host "✓ Backend directories created" -ForegroundColor Green

Write-Host "Step 4: Copying server files to backend..." -ForegroundColor Yellow

# Copy server files
if (Test-Path "server") {
    Copy-Item "server/*" "backend/" -Force -Recurse -ErrorAction SilentlyContinue
}

# Copy shared files
if (Test-Path "shared") {
    Copy-Item "shared" "backend/" -Force -Recurse -ErrorAction SilentlyContinue
}

Write-Host "✓ Backend files copied" -ForegroundColor Green

Write-Host "Step 5: Creating package.json files..." -ForegroundColor Yellow

# Create frontend package.json
$frontendPackageJson = @"
{
  "name": "nacinema-frontend",
  "version": "1.0.0",
  "description": "NaCinema Frontend - Movie Ticket Booking System (Vanilla JavaScript)",
  "main": "index.html",
  "scripts": {
    "dev": "npx http-server . -p 3000 -c-1 --cors",
    "start": "npx http-server . -p 3000 --cors",
    "build": "echo 'No build step required for vanilla version'",
    "test": "echo 'Open test-features.html in browser'",
    "preview": "npx http-server . -p 4173 --cors"
  },
  "keywords": [
    "cinema",
    "movie",
    "booking",
    "vanilla-js",
    "frontend"
  ],
  "author": "NaCinema Team",
  "license": "MIT",
  "devDependencies": {
    "http-server": "^14.1.1"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
"@

$frontendPackageJson | Out-File -FilePath "frontend/package.json" -Encoding UTF8

# Create backend package.json
$backendPackageJson = @"
{
  "name": "nacinema-backend",
  "version": "1.0.0",
  "description": "NaCinema Backend - Movie Ticket Booking API (Node.js/Express)",
  "main": "server.js",
  "scripts": {
    "dev": "tsx watch server.js",
    "start": "node server.js",
    "build": "tsc",
    "test": "echo 'No tests specified'"
  },
  "keywords": [
    "cinema",
    "movie",
    "booking",
    "nodejs",
    "express",
    "api",
    "backend"
  ],
  "author": "NaCinema Team",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "bcrypt": "^5.1.1",
    "jsonwebtoken": "^9.0.2",
    "mongodb": "^6.3.0",
    "express-session": "^1.17.3",
    "passport": "^0.7.0",
    "passport-local": "^1.0.0"
  },
  "devDependencies": {
    "tsx": "^4.7.0",
    "typescript": "^5.3.3",
    "@types/node": "^20.10.6",
    "@types/express": "^4.17.21",
    "@types/bcrypt": "^5.0.2",
    "@types/jsonwebtoken": "^9.0.5"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
"@

$backendPackageJson | Out-File -FilePath "backend/package.json" -Encoding UTF8

Write-Host "✓ Package.json files created" -ForegroundColor Green

Write-Host "Step 6: Creating main server file..." -ForegroundColor Yellow

# Create main server.js for backend
$serverJs = @"
const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:3000',  // Frontend dev server
    'http://localhost:4173',  // Frontend preview
    'http://127.0.0.1:3000',
    'http://127.0.0.1:4173'
  ],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'NaCinema Backend API'
  });
});

// API routes
try {
  const routes = require('./routes');
  app.use('/api', routes);
} catch (error) {
  console.warn('Routes not found, using basic setup');
  
  // Basic routes for testing
  app.get('/api/movies', (req, res) => {
    res.json([
      {
        id: 1,
        title: 'Sample Movie',
        genre: 'Action',
        duration: 120,
        poster: '/assets/images/sample-movie.jpg',
        description: 'This is a sample movie for testing.',
        status: 'active'
      }
    ]);
  });
  
  app.get('/api/auth/me', (req, res) => {
    res.json({ user: null });
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

app.listen(PORT, () => {
  console.log(`🚀 NaCinema Backend API running on http://localhost:`+PORT);
  console.log(`📊 Health check: http://localhost:`+PORT+`/health`);
  console.log(`🎬 Movies API: http://localhost:`+PORT+`/api/movies`);
});

module.exports = app;
"@

$serverJs | Out-File -FilePath "backend/server.js" -Encoding UTF8

Write-Host "✓ Main server file created" -ForegroundColor Green

Write-Host "Step 7: Creating environment files..." -ForegroundColor Yellow

# Create backend .env
$backendEnv = @"
# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/nacinema

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production

# File Upload Configuration
MAX_FILE_SIZE=10mb
UPLOAD_PATH=./uploads
"@

$backendEnv | Out-File -FilePath "backend/.env" -Encoding UTF8

Write-Host "✓ Environment files created" -ForegroundColor Green

Write-Host "Step 8: Creating development scripts..." -ForegroundColor Yellow

# Create development start script
$devStartBat = @"
@echo off
echo ========================================
echo   Starting NaCinema Development Servers
echo ========================================
echo.

echo Starting Backend Server...
start "NaCinema Backend" cmd /k "cd backend && npm run dev"

echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo Starting Frontend Server...
start "NaCinema Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ✓ Both servers started!
echo.
echo 🔧 Backend API: http://localhost:5000
echo 🌐 Frontend:    http://localhost:3000
echo 📊 Health:      http://localhost:5000/health
echo.
echo Press any key to exit...
pause >nul
"@

$devStartBat | Out-File -FilePath "dev-start.bat" -Encoding UTF8

Write-Host "✓ Development scripts created" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🎉 SEPARATION COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📁 Project structure:" -ForegroundColor White
Write-Host "  ├── frontend/     (Vanilla JavaScript)" -ForegroundColor Gray
Write-Host "  ├── backend/      (Node.js/Express)" -ForegroundColor Gray
Write-Host "  └── dev-start.bat (Development script)" -ForegroundColor Gray
Write-Host ""

Write-Host "🚀 Next steps:" -ForegroundColor White
Write-Host "  1. Install frontend dependencies:" -ForegroundColor Gray
Write-Host "     cd frontend && npm install" -ForegroundColor Yellow
Write-Host ""
Write-Host "  2. Install backend dependencies:" -ForegroundColor Gray
Write-Host "     cd backend && npm install" -ForegroundColor Yellow
Write-Host ""
Write-Host "  3. Start both servers:" -ForegroundColor Gray
Write-Host "     ./dev-start.bat" -ForegroundColor Yellow
Write-Host ""
Write-Host "  4. Or start individually:" -ForegroundColor Gray
Write-Host "     Backend: cd backend && npm run dev" -ForegroundColor Yellow
Write-Host "     Frontend: cd frontend && npm run dev" -ForegroundColor Yellow
Write-Host ""

Write-Host "🌐 URLs:" -ForegroundColor White
Write-Host "  Frontend: http://localhost:3000" -ForegroundColor Green
Write-Host "  Backend:  http://localhost:5000" -ForegroundColor Green
Write-Host "  Health:   http://localhost:5000/health" -ForegroundColor Green
Write-Host ""

Write-Host "📖 For detailed instructions, see: SEPARATION_GUIDE.md" -ForegroundColor Cyan
Write-Host ""
