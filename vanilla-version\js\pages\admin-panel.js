// ===== ADMIN PANEL PAGE =====

window.pages.adminPanel = {
  currentTab: "statistics",
  data: {
    statistics: {},
    movies: [],
    cinemas: [],
    showtimes: [],
    tickets: [],
    users: [],
  },

  async render() {
    const mainContent = utils.$("#main-content");

    try {
      // Check admin permission
      if (!auth.hasRole("admin")) {
        window.router.navigate("/");
        Toast.error("Bạn không có quyền truy cập trang này");
        return;
      }

      // Show loading
      mainContent.innerHTML = `
                <div class="container">
                    <div class="admin-loading">
                        <div class="spinner"></div>
                        <p>Đang tải dữ liệu quản trị...</p>
                    </div>
                </div>
            `;

      // Load admin data
      await this.loadAdminData();

      // Render admin panel
      this.renderAdminPanel();
      this.setupEventHandlers();
    } catch (error) {
      console.error("Failed to render admin panel:", error);
      this.renderError("Có lỗi xảy ra khi tải trang quản trị.");
    }
  },

  async loadAdminData() {
    try {
      // Load statistics
      this.data.statistics = await api.get("/api/admin/statistics");

      // Load movies
      this.data.movies = await api.get("/api/admin/movies");

      // Load cinemas
      this.data.cinemas = await api.get("/api/admin/cinemas");

      // Load showtimes
      this.data.showtimes = await api.get("/api/admin/showtimes");

      // Load tickets
      this.data.tickets = await api.get("/api/admin/tickets");

      // Load users
      this.data.users = await api.get("/api/admin/users");
    } catch (error) {
      console.error("Failed to load admin data:", error);
      // Set default empty data
      Object.keys(this.data).forEach(key => {
        if (Array.isArray(this.data[key])) {
          this.data[key] = [];
        } else {
          this.data[key] = {};
        }
      });
    }
  },

  renderAdminPanel() {
    const mainContent = utils.$("#main-content");

    mainContent.innerHTML = `
            <div class="admin-panel">
                <div class="container">
                    <!-- Admin Header -->
                    <div class="admin-header">
                        <div class="admin-title">
                            <h1><i class="fas fa-cog"></i> Quản trị hệ thống</h1>
                            <p>Quản lý toàn bộ hệ thống đặt vé xem phim</p>
                        </div>
                        <div class="admin-user">
                            <span>Xin chào, ${
                              auth.getCurrentUser().fullName
                            }</span>
                            <button class="btn btn-secondary" onclick="auth.logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                Đăng xuất
                            </button>
                        </div>
                    </div>

                    <!-- Admin Tabs -->
                    <div class="admin-tabs">
                        <div class="tab-list">
                            <button class="tab-button ${
                              this.currentTab === "statistics" ? "active" : ""
                            }" data-tab="statistics">
                                <i class="fas fa-chart-bar"></i>
                                Thống kê
                            </button>
                            <button class="tab-button ${
                              this.currentTab === "movies" ? "active" : ""
                            }" data-tab="movies">
                                <i class="fas fa-film"></i>
                                Quản lý phim
                            </button>
                            <button class="tab-button ${
                              this.currentTab === "cinemas" ? "active" : ""
                            }" data-tab="cinemas">
                                <i class="fas fa-building"></i>
                                Quản lý rạp
                            </button>
                            <button class="tab-button ${
                              this.currentTab === "showtimes" ? "active" : ""
                            }" data-tab="showtimes">
                                <i class="fas fa-calendar"></i>
                                Suất chiếu
                            </button>
                            <button class="tab-button ${
                              this.currentTab === "tickets" ? "active" : ""
                            }" data-tab="tickets">
                                <i class="fas fa-ticket-alt"></i>
                                Quản lý vé
                            </button>
                            <button class="tab-button ${
                              this.currentTab === "users" ? "active" : ""
                            }" data-tab="users">
                                <i class="fas fa-users"></i>
                                Người dùng
                            </button>
                        </div>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <div class="tab-pane ${
                              this.currentTab === "statistics" ? "active" : ""
                            }" id="statistics-tab">
                                ${this.renderStatisticsTab()}
                            </div>
                            <div class="tab-pane ${
                              this.currentTab === "movies" ? "active" : ""
                            }" id="movies-tab">
                                ${this.renderMoviesTab()}
                            </div>
                            <div class="tab-pane ${
                              this.currentTab === "cinemas" ? "active" : ""
                            }" id="cinemas-tab">
                                ${this.renderCinemasTab()}
                            </div>
                            <div class="tab-pane ${
                              this.currentTab === "showtimes" ? "active" : ""
                            }" id="showtimes-tab">
                                ${this.renderShowtimesTab()}
                            </div>
                            <div class="tab-pane ${
                              this.currentTab === "tickets" ? "active" : ""
                            }" id="tickets-tab">
                                ${this.renderTicketsTab()}
                            </div>
                            <div class="tab-pane ${
                              this.currentTab === "users" ? "active" : ""
                            }" id="users-tab">
                                ${this.renderUsersTab()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
  },

  renderStatisticsTab() {
    const stats = this.data.statistics;

    return `
            <div class="statistics-section">
                <div class="stats-header">
                    <h3>Tổng quan hệ thống</h3>
                    <div class="stats-period">
                        <select class="form-select" id="stats-period">
                            <option value="today">Hôm nay</option>
                            <option value="week">Tuần này</option>
                            <option value="month" selected>Tháng này</option>
                            <option value="year">Năm này</option>
                        </select>
                    </div>
                </div>

                <!-- Key Metrics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon revenue">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Doanh thu</h4>
                            <div class="stat-value">${utils.formatPrice(
                              stats.revenue || 0
                            )}</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +12.5% so với tháng trước
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon tickets">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Vé đã bán</h4>
                            <div class="stat-value">${
                              stats.ticketsSold || 0
                            }</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +8.3% so với tháng trước
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Người dùng mới</h4>
                            <div class="stat-value">${stats.newUsers || 0}</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +15.7% so với tháng trước
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon movies">
                            <i class="fas fa-film"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Phim đang chiếu</h4>
                            <div class="stat-value">${
                              stats.activeMovies || 0
                            }</div>
                            <div class="stat-change neutral">
                                <i class="fas fa-minus"></i>
                                Không đổi
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-section">
                    <div class="chart-container">
                        <h4>Doanh thu theo ngày</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line"></i>
                            <p>Biểu đồ doanh thu sẽ được hiển thị ở đây</p>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h4>Top phim bán chạy</h4>
                        <div class="top-movies-list">
                            ${(stats.topMovies || [])
                              .map(
                                (movie, index) => `
                                <div class="top-movie-item">
                                    <span class="rank">#${index + 1}</span>
                                    <span class="movie-name">${
                                      movie.title
                                    }</span>
                                    <span class="movie-tickets">${
                                      movie.ticketsSold
                                    } vé</span>
                                </div>
                            `
                              )
                              .join("")}
                        </div>
                    </div>
                </div>
            </div>
        `;
  },

  renderMoviesTab() {
    return `
            <div class="movies-section">
                <div class="section-header">
                    <h3>Quản lý phim</h3>
                    <button class="btn btn-primary" id="add-movie-btn">
                        <i class="fas fa-plus"></i>
                        Thêm phim mới
                    </button>
                </div>

                <div class="movies-filters">
                    <div class="filter-group">
                        <select class="form-select" id="movie-status-filter">
                            <option value="all">Tất cả trạng thái</option>
                            <option value="active">Đang chiếu</option>
                            <option value="coming-soon">Sắp chiếu</option>
                            <option value="ended">Đã kết thúc</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <input type="text" class="form-input" placeholder="Tìm kiếm phim..." id="movie-search">
                    </div>
                </div>

                <div class="movies-table">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Poster</th>
                                <th>Tên phim</th>
                                <th>Thể loại</th>
                                <th>Thời lượng</th>
                                <th>Trạng thái</th>
                                <th>Ngày khởi chiếu</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.data.movies
                              .map(
                                movie => `
                                <tr>
                                    <td>
                                        <img src="${movie.poster}" alt="${
                                  movie.title
                                }" class="movie-poster-tiny">
                                    </td>
                                    <td>
                                        <div class="movie-title-cell">
                                            <strong>${movie.title}</strong>
                                            <div class="movie-rating">
                                                <i class="fas fa-star"></i>
                                                ${movie.rating || "N/A"}
                                            </div>
                                        </div>
                                    </td>
                                    <td>${movie.genre}</td>
                                    <td>${movie.duration} phút</td>
                                    <td>
                                        <span class="status-badge status-${
                                          movie.status
                                        }">
                                            ${this.getMovieStatusText(
                                              movie.status
                                            )}
                                        </span>
                                    </td>
                                    <td>${utils.formatDate(
                                      movie.releaseDate
                                    )}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-secondary" onclick="window.pages.adminPanel.editMovie(${
                                              movie.id
                                            })">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="window.pages.adminPanel.viewMovieDetails(${
                                              movie.id
                                            })">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="window.pages.adminPanel.deleteMovie(${
                                              movie.id
                                            })">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `
                              )
                              .join("")}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
  },

  renderCinemasTab() {
    return `
            <div class="cinemas-section">
                <div class="section-header">
                    <h3>Quản lý rạp chiếu</h3>
                    <button class="btn btn-primary" id="add-cinema-btn">
                        <i class="fas fa-plus"></i>
                        Thêm rạp mới
                    </button>
                </div>

                <div class="cinemas-grid">
                    ${this.data.cinemas
                      .map(
                        cinema => `
                        <div class="cinema-card">
                            <div class="cinema-header">
                                <h4>${cinema.name}</h4>
                                <div class="cinema-status">
                                    <span class="status-badge status-${
                                      cinema.status
                                    }">
                                        ${
                                          cinema.status === "active"
                                            ? "Hoạt động"
                                            : "Tạm dừng"
                                        }
                                    </span>
                                </div>
                            </div>
                            <div class="cinema-info">
                                <p><i class="fas fa-map-marker-alt"></i> ${
                                  cinema.address
                                }</p>
                                <p><i class="fas fa-phone"></i> ${
                                  cinema.phone
                                }</p>
                                <p><i class="fas fa-door-open"></i> ${
                                  cinema.rooms?.length || 0
                                } phòng chiếu</p>
                            </div>
                            <div class="cinema-actions">
                                <button class="btn btn-sm btn-secondary" onclick="window.pages.adminPanel.editCinema(${
                                  cinema.id
                                })">
                                    <i class="fas fa-edit"></i>
                                    Sửa
                                </button>
                                <button class="btn btn-sm btn-info" onclick="window.pages.adminPanel.manageCinemaRooms(${
                                  cinema.id
                                })">
                                    <i class="fas fa-door-open"></i>
                                    Phòng chiếu
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="window.pages.adminPanel.deleteCinema(${
                                  cinema.id
                                })">
                                    <i class="fas fa-trash"></i>
                                    Xóa
                                </button>
                            </div>
                        </div>
                    `
                      )
                      .join("")}
                </div>
            </div>
        `;
  },

  renderShowtimesTab() {
    return `
            <div class="showtimes-section">
                <div class="section-header">
                    <h3>Quản lý suất chiếu</h3>
                    <button class="btn btn-primary" id="add-showtime-btn">
                        <i class="fas fa-plus"></i>
                        Thêm suất chiếu
                    </button>
                </div>

                <div class="showtimes-filters">
                    <div class="filter-group">
                        <select class="form-select" id="showtime-cinema-filter">
                            <option value="all">Tất cả rạp</option>
                            ${this.data.cinemas
                              .map(
                                cinema => `
                                <option value="${cinema.id}">${cinema.name}</option>
                            `
                              )
                              .join("")}
                        </select>
                    </div>
                    <div class="filter-group">
                        <input type="date" class="form-input" id="showtime-date-filter">
                    </div>
                </div>

                <div class="showtimes-table">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Phim</th>
                                <th>Rạp</th>
                                <th>Phòng</th>
                                <th>Ngày chiếu</th>
                                <th>Giờ chiếu</th>
                                <th>Giá vé</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.data.showtimes
                              .map(
                                showtime => `
                                <tr>
                                    <td>
                                        <div class="movie-cell">
                                            <img src="${
                                              showtime.movie.poster
                                            }" alt="${
                                  showtime.movie.title
                                }" class="movie-poster-tiny">
                                            <span>${showtime.movie.title}</span>
                                        </div>
                                    </td>
                                    <td>${showtime.cinema.name}</td>
                                    <td>${showtime.room.name}</td>
                                    <td>${utils.formatDate(showtime.date)}</td>
                                    <td>${utils.formatTime(
                                      showtime.startTime
                                    )}</td>
                                    <td>${utils.formatPrice(
                                      showtime.price
                                    )}</td>
                                    <td>
                                        <span class="status-badge status-${
                                          showtime.status
                                        }">
                                            ${this.getShowtimeStatusText(
                                              showtime.status
                                            )}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-secondary" onclick="window.pages.adminPanel.editShowtime(${
                                              showtime.id
                                            })">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="window.pages.adminPanel.viewShowtimeSeats(${
                                              showtime.id
                                            })">
                                                <i class="fas fa-chair"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="window.pages.adminPanel.deleteShowtime(${
                                              showtime.id
                                            })">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `
                              )
                              .join("")}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
  },

  renderTicketsTab() {
    return `
            <div class="tickets-section">
                <div class="section-header">
                    <h3>Quản lý vé</h3>
                    <div class="tickets-stats">
                        <span class="stat-item">
                            <i class="fas fa-ticket-alt"></i>
                            ${this.data.tickets.length} vé
                        </span>
                    </div>
                </div>

                <div class="tickets-filters">
                    <div class="filter-group">
                        <select class="form-select" id="ticket-status-filter">
                            <option value="all">Tất cả trạng thái</option>
                            <option value="confirmed">Đã xác nhận</option>
                            <option value="pending">Chờ thanh toán</option>
                            <option value="cancelled">Đã hủy</option>
                            <option value="used">Đã sử dụng</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <input type="text" class="form-input" placeholder="Tìm theo mã vé..." id="ticket-search">
                    </div>
                </div>

                <div class="tickets-table">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Mã vé</th>
                                <th>Khách hàng</th>
                                <th>Phim</th>
                                <th>Suất chiếu</th>
                                <th>Ghế</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.data.tickets
                              .map(
                                ticket => `
                                <tr>
                                    <td>
                                        <strong>${ticket.code}</strong>
                                        <div class="ticket-date">${utils.formatDate(
                                          ticket.createdAt
                                        )}</div>
                                    </td>
                                    <td>
                                        <div class="customer-cell">
                                            <strong>${
                                              ticket.customer.fullName
                                            }</strong>
                                            <div>${ticket.customer.phone}</div>
                                        </div>
                                    </td>
                                    <td>${ticket.movie.title}</td>
                                    <td>
                                        <div class="showtime-cell">
                                            <div>${utils.formatDate(
                                              ticket.showtime.date
                                            )}</div>
                                            <div>${utils.formatTime(
                                              ticket.showtime.startTime
                                            )}</div>
                                        </div>
                                    </td>
                                    <td>
                                        ${ticket.seats
                                          .map(
                                            seat =>
                                              `<span class="seat-badge">${seat.seatNumber}</span>`
                                          )
                                          .join("")}
                                    </td>
                                    <td>${utils.formatPrice(
                                      ticket.totalAmount
                                    )}</td>
                                    <td>
                                        <span class="status-badge status-${
                                          ticket.status
                                        }">
                                            ${this.getTicketStatusText(
                                              ticket.status
                                            )}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" onclick="window.pages.adminPanel.viewTicketDetails('${
                                              ticket.code
                                            }')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            ${
                                              ticket.status === "pending"
                                                ? `
                                                <button class="btn btn-sm btn-success" onclick="window.pages.adminPanel.confirmTicket('${ticket.code}')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            `
                                                : ""
                                            }
                                            <button class="btn btn-sm btn-danger" onclick="window.pages.adminPanel.cancelTicket('${
                                              ticket.code
                                            }')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `
                              )
                              .join("")}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
  },

  renderUsersTab() {
    return `
            <div class="users-section">
                <div class="section-header">
                    <h3>Quản lý người dùng</h3>
                    <div class="users-stats">
                        <span class="stat-item">
                            <i class="fas fa-users"></i>
                            ${this.data.users.length} người dùng
                        </span>
                    </div>
                </div>

                <div class="users-filters">
                    <div class="filter-group">
                        <select class="form-select" id="user-role-filter">
                            <option value="all">Tất cả vai trò</option>
                            <option value="user">Khách hàng</option>
                            <option value="staff">Nhân viên</option>
                            <option value="admin">Quản trị viên</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <input type="text" class="form-input" placeholder="Tìm kiếm người dùng..." id="user-search">
                    </div>
                </div>

                <div class="users-table">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Tên người dùng</th>
                                <th>Email</th>
                                <th>Số điện thoại</th>
                                <th>Vai trò</th>
                                <th>Ngày đăng ký</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.data.users
                              .map(
                                user => `
                                <tr>
                                    <td>
                                        <div class="user-cell">
                                            <div class="user-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="user-info">
                                                <strong>${
                                                  user.fullName || user.username
                                                }</strong>
                                                <div class="user-id">#${
                                                  user.id
                                                }</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>${user.email}</td>
                                    <td>${user.phone || "Chưa cập nhật"}</td>
                                    <td>
                                        <span class="role-badge role-${
                                          user.role
                                        }">
                                            ${this.getRoleText(user.role)}
                                        </span>
                                    </td>
                                    <td>${utils.formatDate(user.createdAt)}</td>
                                    <td>
                                        <span class="status-badge status-${
                                          user.status
                                        }">
                                            ${
                                              user.status === "active"
                                                ? "Hoạt động"
                                                : "Tạm khóa"
                                            }
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" onclick="window.pages.adminPanel.viewUserDetails(${
                                              user.id
                                            })">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="window.pages.adminPanel.editUser(${
                                              user.id
                                            })">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            ${
                                              user.status === "active"
                                                ? `
                                                <button class="btn btn-sm btn-warning" onclick="window.pages.adminPanel.suspendUser(${user.id})">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            `
                                                : `
                                                <button class="btn btn-sm btn-success" onclick="window.pages.adminPanel.activateUser(${user.id})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            `
                                            }
                                        </div>
                                    </td>
                                </tr>
                            `
                              )
                              .join("")}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
  },

  setupEventHandlers() {
    // Tab switching
    const tabButtons = utils.$$(".tab-button");
    tabButtons.forEach(button => {
      button.addEventListener("click", e => {
        const tab = e.target.closest(".tab-button").dataset.tab;
        this.switchTab(tab);
      });
    });

    // Add buttons
    this.setupAddButtons();

    // Filter handlers
    this.setupFilterHandlers();
  },

  setupAddButtons() {
    const addMovieBtn = utils.$("#add-movie-btn");
    if (addMovieBtn) {
      addMovieBtn.addEventListener("click", () => this.showAddMovieModal());
    }

    const addCinemaBtn = utils.$("#add-cinema-btn");
    if (addCinemaBtn) {
      addCinemaBtn.addEventListener("click", () => this.showAddCinemaModal());
    }

    const addShowtimeBtn = utils.$("#add-showtime-btn");
    if (addShowtimeBtn) {
      addShowtimeBtn.addEventListener("click", () =>
        this.showAddShowtimeModal()
      );
    }
  },

  setupFilterHandlers() {
    // Movie filters
    const movieStatusFilter = utils.$("#movie-status-filter");
    if (movieStatusFilter) {
      movieStatusFilter.addEventListener("change", () => this.filterMovies());
    }

    const movieSearch = utils.$("#movie-search");
    if (movieSearch) {
      movieSearch.addEventListener(
        "input",
        utils.debounce(() => this.filterMovies(), 300)
      );
    }

    // Ticket filters
    const ticketStatusFilter = utils.$("#ticket-status-filter");
    if (ticketStatusFilter) {
      ticketStatusFilter.addEventListener("change", () => this.filterTickets());
    }

    const ticketSearch = utils.$("#ticket-search");
    if (ticketSearch) {
      ticketSearch.addEventListener(
        "input",
        utils.debounce(() => this.filterTickets(), 300)
      );
    }

    // User filters
    const userRoleFilter = utils.$("#user-role-filter");
    if (userRoleFilter) {
      userRoleFilter.addEventListener("change", () => this.filterUsers());
    }

    const userSearch = utils.$("#user-search");
    if (userSearch) {
      userSearch.addEventListener(
        "input",
        utils.debounce(() => this.filterUsers(), 300)
      );
    }
  },

  switchTab(tab) {
    this.currentTab = tab;

    // Update tab buttons
    utils.$$(".tab-button").forEach(btn => btn.classList.remove("active"));
    utils.$(`[data-tab="${tab}"]`).classList.add("active");

    // Update tab content
    utils.$$(".tab-pane").forEach(pane => pane.classList.remove("active"));
    utils.$(`#${tab}-tab`).classList.add("active");
  },

  // Movie management methods
  async showAddMovieModal() {
    const modalContent = `
            <form id="add-movie-form" class="admin-form">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Tên phim *</label>
                        <input type="text" name="title" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Thể loại *</label>
                        <select name="genre" class="form-select" required>
                            <option value="">Chọn thể loại</option>
                            <option value="Hành động">Hành động</option>
                            <option value="Hài">Hài</option>
                            <option value="Kinh dị">Kinh dị</option>
                            <option value="Tình cảm">Tình cảm</option>
                            <option value="Khoa học viễn tưởng">Khoa học viễn tưởng</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Thời lượng (phút) *</label>
                        <input type="number" name="duration" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ngày khởi chiếu *</label>
                        <input type="date" name="releaseDate" class="form-input" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">URL Poster *</label>
                    <input type="url" name="poster" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Mô tả</label>
                    <textarea name="description" class="form-input" rows="4"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Trạng thái</label>
                    <select name="status" class="form-select">
                        <option value="coming-soon">Sắp chiếu</option>
                        <option value="active">Đang chiếu</option>
                        <option value="ended">Đã kết thúc</option>
                    </select>
                </div>
            </form>
        `;

    const confirmed = await Modal.show(modalContent, {
      title: "Thêm phim mới",
      confirmText: "Thêm phim",
      cancelText: "Hủy",
    });

    if (confirmed) {
      await this.addMovie();
    }
  },

  async addMovie() {
    const form = utils.$("#add-movie-form");
    const formData = new FormData(form);

    try {
      const movieData = {
        title: formData.get("title"),
        genre: formData.get("genre"),
        duration: parseInt(formData.get("duration")),
        releaseDate: formData.get("releaseDate"),
        poster: formData.get("poster"),
        description: formData.get("description"),
        status: formData.get("status"),
      };

      await api.post("/api/admin/movies", movieData);
      Toast.success("Thêm phim thành công!");

      // Reload movies data
      this.data.movies = await api.get("/api/admin/movies");
      this.renderAdminPanel();
      this.setupEventHandlers();
    } catch (error) {
      Toast.error("Không thể thêm phim. Vui lòng thử lại.");
    }
  },

  async editMovie(movieId) {
    try {
      const movie = await api.get(`/api/admin/movies/${movieId}`);

      const modalContent = `
                <form id="edit-movie-form" class="admin-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Tên phim *</label>
                            <input type="text" name="title" class="form-input" value="${
                              movie.title
                            }" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Thể loại *</label>
                            <select name="genre" class="form-select" required>
                                <option value="Hành động" ${
                                  movie.genre === "Hành động" ? "selected" : ""
                                }>Hành động</option>
                                <option value="Hài" ${
                                  movie.genre === "Hài" ? "selected" : ""
                                }>Hài</option>
                                <option value="Kinh dị" ${
                                  movie.genre === "Kinh dị" ? "selected" : ""
                                }>Kinh dị</option>
                                <option value="Tình cảm" ${
                                  movie.genre === "Tình cảm" ? "selected" : ""
                                }>Tình cảm</option>
                                <option value="Khoa học viễn tưởng" ${
                                  movie.genre === "Khoa học viễn tưởng"
                                    ? "selected"
                                    : ""
                                }>Khoa học viễn tưởng</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Thời lượng (phút) *</label>
                            <input type="number" name="duration" class="form-input" value="${
                              movie.duration
                            }" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Ngày khởi chiếu *</label>
                            <input type="date" name="releaseDate" class="form-input" value="${
                              movie.releaseDate
                            }" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">URL Poster *</label>
                        <input type="url" name="poster" class="form-input" value="${
                          movie.poster
                        }" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Mô tả</label>
                        <textarea name="description" class="form-input" rows="4">${
                          movie.description || ""
                        }</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Trạng thái</label>
                        <select name="status" class="form-select">
                            <option value="coming-soon" ${
                              movie.status === "coming-soon" ? "selected" : ""
                            }>Sắp chiếu</option>
                            <option value="active" ${
                              movie.status === "active" ? "selected" : ""
                            }>Đang chiếu</option>
                            <option value="ended" ${
                              movie.status === "ended" ? "selected" : ""
                            }>Đã kết thúc</option>
                        </select>
                    </div>
                </form>
            `;

      const confirmed = await Modal.show(modalContent, {
        title: "Chỉnh sửa phim",
        confirmText: "Cập nhật",
        cancelText: "Hủy",
      });

      if (confirmed) {
        await this.updateMovie(movieId);
      }
    } catch (error) {
      Toast.error("Không thể tải thông tin phim.");
    }
  },

  async updateMovie(movieId) {
    const form = utils.$("#edit-movie-form");
    const formData = new FormData(form);

    try {
      const movieData = {
        title: formData.get("title"),
        genre: formData.get("genre"),
        duration: parseInt(formData.get("duration")),
        releaseDate: formData.get("releaseDate"),
        poster: formData.get("poster"),
        description: formData.get("description"),
        status: formData.get("status"),
      };

      await api.put(`/api/admin/movies/${movieId}`, movieData);
      Toast.success("Cập nhật phim thành công!");

      // Reload movies data
      this.data.movies = await api.get("/api/admin/movies");
      this.renderAdminPanel();
      this.setupEventHandlers();
    } catch (error) {
      Toast.error("Không thể cập nhật phim. Vui lòng thử lại.");
    }
  },

  async deleteMovie(movieId) {
    const confirmed = await Modal.confirm(
      "Bạn có chắc chắn muốn xóa phim này? Hành động này không thể hoàn tác.",
      { title: "Xác nhận xóa phim" }
    );

    if (confirmed) {
      try {
        await api.delete(`/api/admin/movies/${movieId}`);
        Toast.success("Xóa phim thành công!");

        // Reload movies data
        this.data.movies = await api.get("/api/admin/movies");
        this.renderAdminPanel();
        this.setupEventHandlers();
      } catch (error) {
        Toast.error("Không thể xóa phim. Vui lòng thử lại.");
      }
    }
  },

  // Utility methods
  getMovieStatusText(status) {
    const statusTexts = {
      active: "Đang chiếu",
      "coming-soon": "Sắp chiếu",
      ended: "Đã kết thúc",
    };
    return statusTexts[status] || status;
  },

  getShowtimeStatusText(status) {
    const statusTexts = {
      active: "Hoạt động",
      cancelled: "Đã hủy",
      completed: "Đã hoàn thành",
    };
    return statusTexts[status] || status;
  },

  getTicketStatusText(status) {
    const statusTexts = {
      confirmed: "Đã xác nhận",
      pending: "Chờ thanh toán",
      cancelled: "Đã hủy",
      used: "Đã sử dụng",
    };
    return statusTexts[status] || status;
  },

  getRoleText(role) {
    const roleTexts = {
      user: "Khách hàng",
      staff: "Nhân viên",
      admin: "Quản trị viên",
    };
    return roleTexts[role] || role;
  },

  renderError(message) {
    const mainContent = utils.$("#main-content");
    mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải trang quản trị</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.pages.adminPanel.render()">
                                Thử lại
                            </button>
                            <a href="#" class="btn btn-secondary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
  },
};
