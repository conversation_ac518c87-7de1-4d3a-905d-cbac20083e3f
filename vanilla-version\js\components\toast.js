// ===== TOAST NOTIFICATION SYSTEM =====

class Toast {
    constructor() {
        this.container = null;
        this.toasts = new Map();
        this.init();
    }

    // Initialize toast system
    init() {
        this.container = utils.$('#toast-container');
        
        if (!this.container) {
            console.error('Toast container not found');
            return;
        }
    }

    // Show toast notification
    show(message, options = {}) {
        const {
            type = 'info',
            duration = 5000,
            closable = true,
            position = 'top-right',
            onClick = null,
            onClose = null
        } = options;

        const toastId = utils.generateId();
        
        // Create toast element
        const toast = this.createToastElement(toastId, message, type, closable);
        
        // Add to container
        this.container.appendChild(toast);
        this.toasts.set(toastId, { element: toast, timer: null, onClose });

        // Position container
        this.container.className = `toast-container toast-${position}`;

        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Setup click handler
        if (onClick) {
            toast.addEventListener('click', onClick);
        }

        // Setup close handler
        const closeBtn = toast.querySelector('.toast-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.hide(toastId);
            });
        }

        // Auto hide
        if (duration > 0) {
            const timer = setTimeout(() => {
                this.hide(toastId);
            }, duration);
            
            this.toasts.get(toastId).timer = timer;
        }

        return toastId;
    }

    // Create toast element
    createToastElement(id, message, type, closable) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.dataset.toastId = id;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${iconMap[type] || iconMap.info}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-message">${message}</div>
            </div>
            ${closable ? '<button class="toast-close"><i class="fas fa-times"></i></button>' : ''}
        `;

        return toast;
    }

    // Hide toast
    hide(toastId) {
        const toastData = this.toasts.get(toastId);
        if (!toastData) return;

        const { element, timer, onClose } = toastData;

        // Clear timer
        if (timer) {
            clearTimeout(timer);
        }

        // Animate out
        element.classList.remove('show');
        element.classList.add('hide');

        // Remove from DOM
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.toasts.delete(toastId);

            // Call onClose callback
            if (onClose) {
                onClose();
            }
        }, 300);
    }

    // Hide all toasts
    hideAll() {
        for (const toastId of this.toasts.keys()) {
            this.hide(toastId);
        }
    }

    // Success toast
    success(message, options = {}) {
        return this.show(message, { ...options, type: 'success' });
    }

    // Error toast
    error(message, options = {}) {
        return this.show(message, { ...options, type: 'error', duration: 7000 });
    }

    // Warning toast
    warning(message, options = {}) {
        return this.show(message, { ...options, type: 'warning', duration: 6000 });
    }

    // Info toast
    info(message, options = {}) {
        return this.show(message, { ...options, type: 'info' });
    }

    // Static methods for easy use
    static show(message, options = {}) {
        if (!window.toast) {
            window.toast = new Toast();
        }
        return window.toast.show(message, options);
    }

    static success(message, options = {}) {
        if (!window.toast) {
            window.toast = new Toast();
        }
        return window.toast.success(message, options);
    }

    static error(message, options = {}) {
        if (!window.toast) {
            window.toast = new Toast();
        }
        return window.toast.error(message, options);
    }

    static warning(message, options = {}) {
        if (!window.toast) {
            window.toast = new Toast();
        }
        return window.toast.warning(message, options);
    }

    static info(message, options = {}) {
        if (!window.toast) {
            window.toast = new Toast();
        }
        return window.toast.info(message, options);
    }

    static hideAll() {
        if (window.toast) {
            window.toast.hideAll();
        }
    }
}

// Initialize toast system
document.addEventListener('DOMContentLoaded', () => {
    window.toast = new Toast();
});

// Export to global scope
window.Toast = Toast;
