# 🔄 Frontend/Backend Separation Guide

## Tổng quan

Hướng dẫn này sẽ giúp bạn tách riêng frontend và backend thành 2 folder độc lập:
- `frontend/` - Vanilla JavaScript frontend
- `backend/` - Node.js/Express backend

## 📁 Cấu trúc mới

```
project-root/
├── frontend/                 # Frontend (Vanilla JS)
│   ├── index.html           # Main HTML file
│   ├── package.json         # Frontend dependencies
│   ├── css/                 # Stylesheets
│   │   ├── main.css
│   │   ├── components.css
│   │   └── responsive.css
│   ├── js/                  # JavaScript files
│   │   ├── app.js          # Main app
│   │   ├── api.js          # API client
│   │   ├── auth.js         # Authentication
│   │   ├── router.js       # Routing
│   │   ├── utils.js        # Utilities
│   │   ├── components/     # UI components
│   │   └── pages/          # Page modules
│   └── assets/             # Static assets
│
├── backend/                 # Backend (Node.js)
│   ├── package.json        # Backend dependencies
│   ├── server.js           # Main server file
│   ├── routes/             # API routes
│   ├── models/             # Data models
│   ├── middleware/         # Express middleware
│   ├── config/             # Configuration
│   └── utils/              # Server utilities
│
└── shared/                  # Shared types/schemas
    └── schema.ts
```

## 🚀 Bước 1: Tạo Frontend

### 1.1 Copy files từ vanilla-version
```bash
# Tạo frontend directory
mkdir frontend

# Copy tất cả files từ vanilla-version
cp -r vanilla-version/* frontend/

# Hoặc trên Windows
xcopy vanilla-version frontend\ /E /I
```

### 1.2 Tạo package.json cho frontend
```json
{
  "name": "nacinema-frontend",
  "version": "1.0.0",
  "description": "NaCinema Frontend - Vanilla JavaScript",
  "main": "index.html",
  "scripts": {
    "dev": "npx http-server . -p 3000 -c-1 --cors",
    "start": "npx http-server . -p 3000 --cors",
    "build": "echo 'No build step required'",
    "preview": "npx http-server . -p 4173 --cors"
  },
  "devDependencies": {
    "http-server": "^14.1.1"
  }
}
```

### 1.3 Cập nhật API endpoints
Trong `frontend/js/api.js`, cập nhật base URL:
```javascript
const API_BASE_URL = 'http://localhost:5000'; // Backend server
```

## 🔧 Bước 2: Tạo Backend

### 2.1 Copy server files
```bash
# Tạo backend directory
mkdir backend

# Copy server files
cp -r server/* backend/
cp shared/ backend/shared/
```

### 2.2 Tạo package.json cho backend
```json
{
  "name": "nacinema-backend",
  "version": "1.0.0",
  "description": "NaCinema Backend - Node.js/Express API",
  "main": "server.js",
  "scripts": {
    "dev": "tsx watch server.js",
    "start": "node server.js",
    "build": "tsc",
    "test": "echo 'No tests specified'"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "bcrypt": "^5.1.1",
    "jsonwebtoken": "^9.0.2",
    "mongodb": "^6.3.0"
  },
  "devDependencies": {
    "tsx": "^4.7.0",
    "typescript": "^5.3.3",
    "@types/node": "^20.10.6"
  }
}
```

### 2.3 Tạo server.js chính
```javascript
const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:4173'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api', require('./routes'));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🚀 Backend server running on http://localhost:${PORT}`);
});
```

## 📋 Bước 3: Cấu hình CORS và API

### 3.1 Cập nhật CORS trong backend
```javascript
// backend/server.js
app.use(cors({
  origin: [
    'http://localhost:3000',  // Frontend dev server
    'http://localhost:4173',  // Frontend preview
    'https://your-domain.com' // Production domain
  ],
  credentials: true
}));
```

### 3.2 Cập nhật API client trong frontend
```javascript
// frontend/js/api.js
const API_CONFIG = {
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'https://your-api-domain.com' 
    : 'http://localhost:5000',
  timeout: 10000
};
```

## 🔄 Bước 4: Development Workflow

### 4.1 Chạy Backend
```bash
cd backend
npm install
npm run dev
# Server chạy tại http://localhost:5000
```

### 4.2 Chạy Frontend
```bash
cd frontend
npm install
npm run dev
# Frontend chạy tại http://localhost:3000
```

### 4.3 Script tự động (tùy chọn)
Tạo `dev-start.bat` trong root:
```batch
@echo off
echo Starting NaCinema Development Servers...

start "Backend" cmd /k "cd backend && npm run dev"
timeout /t 3
start "Frontend" cmd /k "cd frontend && npm run dev"

echo ✓ Both servers started!
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
```

## 🚀 Bước 5: Production Deployment

### 5.1 Frontend (Static hosting)
```bash
cd frontend
# Deploy to Netlify, Vercel, or any static host
# No build step required - just upload files
```

### 5.2 Backend (Server hosting)
```bash
cd backend
npm install --production
npm start
# Deploy to Heroku, Railway, DigitalOcean, etc.
```

## 🔧 Environment Variables

### Frontend (.env - optional)
```
VITE_API_URL=http://localhost:5000
VITE_APP_NAME=NaCinema
```

### Backend (.env)
```
PORT=5000
NODE_ENV=development
JWT_SECRET=your-jwt-secret
MONGODB_URI=mongodb://localhost:27017/nacinema
CORS_ORIGIN=http://localhost:3000
```

## 📝 Checklist

### Frontend Setup
- [ ] Copy vanilla-version files to frontend/
- [ ] Create frontend/package.json
- [ ] Update API base URL in api.js
- [ ] Test frontend runs on http://localhost:3000
- [ ] Verify all pages load correctly

### Backend Setup
- [ ] Copy server files to backend/
- [ ] Create backend/package.json
- [ ] Create main server.js file
- [ ] Configure CORS for frontend origin
- [ ] Test backend runs on http://localhost:5000
- [ ] Verify API endpoints work

### Integration
- [ ] Frontend can call backend APIs
- [ ] Authentication works across both
- [ ] File uploads work (if any)
- [ ] Error handling works properly
- [ ] CORS configured correctly

## 🎯 Benefits

✅ **Separation of Concerns**: Frontend và backend hoàn toàn độc lập
✅ **Scalability**: Có thể scale từng phần riêng biệt
✅ **Deployment**: Deploy frontend và backend ở các server khác nhau
✅ **Development**: Team có thể làm việc độc lập trên từng phần
✅ **Technology**: Có thể thay đổi tech stack của từng phần
✅ **Maintenance**: Dễ maintain và debug hơn

## 🔍 Troubleshooting

### CORS Issues
```javascript
// Thêm vào backend/server.js
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', true);
  next();
});
```

### API Connection Issues
```javascript
// Kiểm tra trong frontend/js/api.js
const testConnection = async () => {
  try {
    const response = await fetch('http://localhost:5000/health');
    console.log('Backend connection:', response.ok ? 'OK' : 'Failed');
  } catch (error) {
    console.error('Backend not reachable:', error);
  }
};
```

## 🎉 Hoàn thành!

Sau khi hoàn tất các bước trên, bạn sẽ có:
- ✅ Frontend độc lập chạy trên port 3000
- ✅ Backend độc lập chạy trên port 5000  
- ✅ API communication hoạt động tốt
- ✅ Cấu trúc project rõ ràng và dễ maintain
- ✅ Sẵn sàng cho production deployment

**Chúc mừng! Bạn đã tách thành công frontend và backend! 🚀**
