// ===== VANILLA ROUTER SYSTEM =====

class VanillaRouter {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.beforeRouteChange = null;
        this.afterRouteChange = null;
        
        // Initialize router
        this.init();
    }

    // Initialize router
    init() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            this.handleRoute(window.location.pathname);
        });

        // Handle initial route
        this.handleRoute(window.location.pathname);

        // Setup link click handlers
        this.setupLinkHandlers();
    }

    // Register a route
    route(path, handler, options = {}) {
        this.routes.set(path, {
            handler,
            ...options
        });
    }

    // Navigate to a route
    navigate(path, replace = false) {
        if (path === this.currentRoute) return;

        // Update browser history
        if (replace) {
            window.history.replaceState({}, '', path);
        } else {
            window.history.pushState({}, '', path);
        }

        // Handle the route
        this.handleRoute(path);
    }

    // Handle route change
    async handleRoute(path) {
        // Call before route change hook
        if (this.beforeRouteChange) {
            const shouldContinue = await this.beforeRouteChange(path, this.currentRoute);
            if (shouldContinue === false) return;
        }

        // Check route guard
        if (window.routeGuard && !window.routeGuard.handleRouteAccess(path)) {
            return;
        }

        // Find matching route
        const route = this.findRoute(path);
        
        if (route) {
            try {
                // Show loading
                this.showLoading();

                // Execute route handler
                await route.handler(this.extractParams(route.path, path));

                // Update current route
                this.currentRoute = path;

                // Update active navigation links
                this.updateActiveLinks(path);

                // Hide loading
                this.hideLoading();

                // Call after route change hook
                if (this.afterRouteChange) {
                    this.afterRouteChange(path);
                }

            } catch (error) {
                console.error('Route handler error:', error);
                this.showError('Có lỗi xảy ra khi tải trang');
                this.hideLoading();
            }
        } else {
            // Handle 404
            this.handle404(path);
        }
    }

    // Find matching route
    findRoute(path) {
        // Exact match first
        if (this.routes.has(path)) {
            return { ...this.routes.get(path), path };
        }

        // Pattern matching for dynamic routes
        for (const [routePath, routeConfig] of this.routes) {
            if (this.matchRoute(routePath, path)) {
                return { ...routeConfig, path: routePath };
            }
        }

        return null;
    }

    // Check if route pattern matches path
    matchRoute(pattern, path) {
        // Convert pattern to regex
        const regexPattern = pattern
            .replace(/:[^/]+/g, '([^/]+)')  // :id -> ([^/]+)
            .replace(/\*/g, '.*');          // * -> .*

        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(path);
    }

    // Extract parameters from path
    extractParams(pattern, path) {
        const params = {};
        const patternParts = pattern.split('/');
        const pathParts = path.split('/');

        for (let i = 0; i < patternParts.length; i++) {
            const patternPart = patternParts[i];
            const pathPart = pathParts[i];

            if (patternPart.startsWith(':')) {
                const paramName = patternPart.substring(1);
                params[paramName] = pathPart;
            }
        }

        return params;
    }

    // Setup click handlers for navigation links
    setupLinkHandlers() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('[data-route]');
            if (link) {
                e.preventDefault();
                const route = link.getAttribute('data-route');
                this.navigate(route);
            }
        });
    }

    // Update active navigation links
    updateActiveLinks(currentPath) {
        // Remove active class from all nav links
        const navLinks = utils.$$('.nav-link, .mobile-nav-link');
        navLinks.forEach(link => link.classList.remove('active'));

        // Add active class to current route link
        const activeLinks = utils.$$(`[data-route="${currentPath}"]`);
        activeLinks.forEach(link => {
            if (link.classList.contains('nav-link') || link.classList.contains('mobile-nav-link')) {
                link.classList.add('active');
            }
        });

        // Handle home route special case
        if (currentPath === '/') {
            const homeLinks = utils.$$('[data-route="/"]');
            homeLinks.forEach(link => {
                if (link.classList.contains('nav-link') || link.classList.contains('mobile-nav-link')) {
                    link.classList.add('active');
                }
            });
        }
    }

    // Show loading spinner
    showLoading() {
        const spinner = utils.$('#loading-spinner');
        if (spinner) {
            spinner.style.display = 'flex';
        }
    }

    // Hide loading spinner
    hideLoading() {
        const spinner = utils.$('#loading-spinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
    }

    // Show error message
    showError(message) {
        const mainContent = utils.$('#main-content');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="container">
                    <div class="error-page">
                        <div class="error-content">
                            <i class="fas fa-exclamation-triangle error-icon"></i>
                            <h2>Oops! Có lỗi xảy ra</h2>
                            <p>${message}</p>
                            <button class="btn btn-primary" onclick="window.location.reload()">
                                Thử lại
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // Handle 404 errors
    handle404(path) {
        const mainContent = utils.$('#main-content');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="container">
                    <div class="error-page">
                        <div class="error-content">
                            <i class="fas fa-search error-icon"></i>
                            <h2>404 - Không tìm thấy trang</h2>
                            <p>Trang bạn đang tìm kiếm không tồn tại.</p>
                            <p class="error-path">Đường dẫn: <code>${path}</code></p>
                            <div class="error-actions">
                                <a href="#" class="btn btn-primary" data-route="/">
                                    Về trang chủ
                                </a>
                                <button class="btn btn-secondary" onclick="window.history.back()">
                                    Quay lại
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        this.hideLoading();
    }

    // Set before route change hook
    beforeEach(callback) {
        this.beforeRouteChange = callback;
    }

    // Set after route change hook
    afterEach(callback) {
        this.afterRouteChange = callback;
    }

    // Get current route
    getCurrentRoute() {
        return this.currentRoute;
    }

    // Check if route is active
    isActive(path) {
        return this.currentRoute === path;
    }

    // Redirect to route
    redirect(from, to) {
        this.route(from, () => {
            this.navigate(to, true);
        });
    }

    // Go back in history
    back() {
        window.history.back();
    }

    // Go forward in history
    forward() {
        window.history.forward();
    }

    // Replace current route
    replace(path) {
        this.navigate(path, true);
    }
}

// ===== ROUTE DEFINITIONS =====

const setupRoutes = (router) => {
    // Home page
    router.route('/', async () => {
        await window.pages.home.render();
    });

    // Movie detail page
    router.route('/movies/:id', async (params) => {
        await window.pages.movieDetail.render(params.id);
    });

    // Login page
    router.route('/login', async () => {
        if (auth.isAuthenticated()) {
            router.navigate('/');
            return;
        }
        await window.pages.login.render();
    });

    // Register page
    router.route('/register', async () => {
        if (auth.isAuthenticated()) {
            router.navigate('/');
            return;
        }
        await window.pages.register.render();
    });

    // User bookings
    router.route('/bookings', async () => {
        await window.pages.bookings.render();
    });

    // User profile
    router.route('/profile', async () => {
        await window.pages.profile.render();
    });

    // Admin panel
    router.route('/admin', async () => {
        await window.pages.admin.render();
    });

    // Staff panel
    router.route('/staff', async () => {
        await window.pages.staff.render();
    });

    // Coming soon movies
    router.route('/coming-soon', async () => {
        await window.pages.comingSoon.render();
    });

    // Cinemas
    router.route('/cinemas', async () => {
        await window.pages.cinemas.render();
    });

    // Promotions
    router.route('/promotions', async () => {
        await window.pages.promotions.render();
    });

    // Booking flow
    router.route('/booking/:showtimeId', async (params) => {
        await window.pages.booking.render(params.showtimeId);
    });

    // Booking success
    router.route('/booking/success/:bookingCode', async (params) => {
        await window.pages.bookingSuccess.render(params.bookingCode);
    });
};

// Create global router instance
const router = new VanillaRouter();

// Setup routes
setupRoutes(router);

// Export to global scope
window.router = router;
