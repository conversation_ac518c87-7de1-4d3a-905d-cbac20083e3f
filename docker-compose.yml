version: '3.8'

services:
  # MongoDB service
  mongo:
    image: mongo:7.0
    container_name: nacinema_mongo
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: nacinema123
      MONGO_INITDB_DATABASE: nacinema
    volumes:
      - mongo_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    ports:
      - "27017:27017"
    networks:
      - nacinema_network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis service (for caching)
  redis:
    image: redis:7-alpine
    container_name: nacinema_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nacinema_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NaCinema application
  app:
    build: .
    container_name: nacinema_app
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - MONGODB_URI=*****************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-for-production-change-this
      - PORT=5000
    ports:
      - "5000:5000"
    volumes:
      - ./uploads:/app/uploads
    networks:
      - nacinema_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: nacinema_nginx
    restart: always
    depends_on:
      - app
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - nacinema_network

volumes:
  mongo_data:
  redis_data:

networks:
  nacinema_network:
    driver: bridge