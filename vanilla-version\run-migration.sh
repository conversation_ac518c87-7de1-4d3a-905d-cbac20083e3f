#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "========================================"
echo "  NaCinema React to Vanilla Migration"
echo "========================================"
echo

echo -e "${BLUE}[INFO]${NC} Starting migration process..."
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}[ERROR]${NC} Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo -e "${BLUE}[INFO]${NC} Node.js detected: $(node --version)"
echo

echo -e "${BLUE}[INFO]${NC} Checking vanilla version..."

# Check if vanilla files exist
if [ ! -f "index.html" ]; then
    echo -e "${RED}[ERROR]${NC} index.html not found in vanilla-version directory"
    echo "Please ensure you are in the correct directory"
    exit 1
fi

if [ ! -f "js/app.js" ]; then
    echo -e "${RED}[ERROR]${NC} js/app.js not found"
    echo "Please ensure vanilla version is complete"
    exit 1
fi

echo -e "${GREEN}[SUCCESS]${NC} Vanilla files found!"
echo

echo -e "${BLUE}[INFO]${NC} Testing vanilla version..."
echo "Opening test page in browser..."
echo "Please test all features before continuing..."
echo

# Open test page in browser (cross-platform)
if command -v xdg-open &> /dev/null; then
    xdg-open "test-features.html"
elif command -v open &> /dev/null; then
    open "test-features.html"
elif command -v start &> /dev/null; then
    start "test-features.html"
else
    echo -e "${YELLOW}[WARNING]${NC} Could not open browser automatically"
    echo "Please manually open test-features.html in your browser"
fi

echo
echo "========================================"
echo "  IMPORTANT: Manual Testing Required"
echo "========================================"
echo
echo "1. A test page should open in your browser"
echo "2. Please run all tests and verify they pass"
echo "3. Test the main application functionality:"
echo "   - Navigation between pages"
echo "   - Movie browsing"
echo "   - User authentication"
echo "   - Booking flow (if backend is running)"
echo "   - Responsive design"
echo

read -p "Have you completed testing and everything works? (y/N): " continue
if [[ ! "$continue" =~ ^[Yy]([Ee][Ss])?$ ]]; then
    echo -e "${BLUE}[INFO]${NC} Migration cancelled by user"
    echo "Please fix any issues and run this script again"
    exit 0
fi

echo
echo -e "${BLUE}[INFO]${NC} User confirmed tests passed"
echo

echo -e "${BLUE}[INFO]${NC} Creating backup of React code..."
echo "This may take a few minutes..."
echo

# Run the migration script
node migrate-to-vanilla.js

if [ $? -ne 0 ]; then
    echo -e "${RED}[ERROR]${NC} Migration script failed"
    echo "Please check the error messages above"
    exit 1
fi

echo
echo "========================================"
echo "  Migration Completed Successfully!"
echo "========================================"
echo
echo -e "${GREEN}[SUCCESS]${NC} React code has been backed up"
echo -e "${GREEN}[SUCCESS]${NC} Vanilla version moved to root directory"
echo -e "${GREEN}[SUCCESS]${NC} React files cleaned up"
echo -e "${GREEN}[SUCCESS]${NC} New package.json created"
echo -e "${GREEN}[SUCCESS]${NC} README.md updated"
echo
echo "Next steps:"
echo "1. cd .. (go to root directory)"
echo "2. npm install (install new dependencies)"
echo "3. npm run dev (start development server)"
echo
echo "Your React code is safely backed up in react-backup-* folder"
echo

read -p "Press Enter to continue..."
