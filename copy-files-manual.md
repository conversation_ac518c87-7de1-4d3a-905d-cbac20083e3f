# Manual File Copy Instructions

## Tách Frontend và Backend thành 2 folder riêng

### Bước 1: Copy Frontend Files

#### 1.1 Copy CSS files
```
vanilla-version/css/main.css → frontend/css/main.css ✓ (đã copy)
vanilla-version/css/components.css → frontend/css/components.css
vanilla-version/css/responsive.css → frontend/css/responsive.css
```

#### 1.2 Copy JS core files
```
vanilla-version/js/app.js → frontend/js/app.js
vanilla-version/js/api.js → frontend/js/api.js
vanilla-version/js/auth.js → frontend/js/auth.js
vanilla-version/js/router.js → frontend/js/router.js
vanilla-version/js/utils.js → frontend/js/utils.js
```

#### 1.3 Copy JS components
```
vanilla-version/js/components/navigation.js → frontend/js/components/navigation.js
vanilla-version/js/components/modal.js → frontend/js/components/modal.js
vanilla-version/js/components/toast.js → frontend/js/components/toast.js
vanilla-version/js/components/seatmap.js → frontend/js/components/seatmap.js
vanilla-version/js/components/movie-card.js → frontend/js/components/movie-card.js
vanilla-version/js/components/booking-form.js → frontend/js/components/booking-form.js
vanilla-version/js/components/movie-reviews.js → frontend/js/components/movie-reviews.js
```

#### 1.4 Copy JS pages
```
vanilla-version/js/pages/home.js → frontend/js/pages/home.js
vanilla-version/js/pages/movie-detail.js → frontend/js/pages/movie-detail.js
vanilla-version/js/pages/login.js → frontend/js/pages/login.js
vanilla-version/js/pages/register.js → frontend/js/pages/register.js
vanilla-version/js/pages/booking.js → frontend/js/pages/booking.js
vanilla-version/js/pages/booking-success.js → frontend/js/pages/booking-success.js
vanilla-version/js/pages/dashboard.js → frontend/js/pages/dashboard.js
vanilla-version/js/pages/admin-panel.js → frontend/js/pages/admin-panel.js
vanilla-version/js/pages/coming-soon.js → frontend/js/pages/coming-soon.js
vanilla-version/js/pages/cinemas.js → frontend/js/pages/cinemas.js
vanilla-version/js/pages/promotions.js → frontend/js/pages/promotions.js
vanilla-version/js/pages/not-found.js → frontend/js/pages/not-found.js
vanilla-version/js/pages/admin.js → frontend/js/pages/admin.js
```

#### 1.5 Copy other files
```
vanilla-version/test-features.html → frontend/test-features.html
vanilla-version/final-test.html → frontend/final-test.html
```

### Bước 2: Copy Backend Files

#### 2.1 Copy server files
```
server/index.ts → backend/index.ts
server/mongodb.ts → backend/mongodb.ts
server/routes.ts → backend/routes.ts
server/storage.ts → backend/storage.ts
server/vite.ts → backend/vite.ts
```

#### 2.2 Copy shared files
```
shared/schema.ts → backend/shared/schema.ts
```

### Bước 3: Tạo các file cấu hình

#### 3.1 Frontend package.json ✓ (đã tạo)
#### 3.2 Backend package.json (cần tạo)
#### 3.3 Backend server.js (cần tạo)
#### 3.4 Backend .env (cần tạo)

### Bước 4: Cập nhật API endpoints

Trong `frontend/js/api.js`, cập nhật:
```javascript
const API_BASE_URL = 'http://localhost:5000'; // Backend server
```

### Bước 5: Test

1. **Backend**: `cd backend && npm install && npm run dev`
2. **Frontend**: `cd frontend && npm install && npm run dev`

### Cấu trúc cuối cùng:

```
project-root/
├── frontend/                 # ✓ Đã tạo
│   ├── index.html           # ✓ Đã có
│   ├── package.json         # ✓ Đã có
│   ├── css/                 # Cần copy
│   ├── js/                  # Cần copy
│   └── assets/              # Cần tạo
│
├── backend/                 # ✓ Đã tạo
│   ├── package.json         # Cần tạo
│   ├── server.js           # Cần tạo
│   ├── .env                # Cần tạo
│   └── ... (server files)   # Cần copy
│
└── dev-start.bat           # Script để start cả 2
```

## Status:
- ✅ Frontend structure created
- ✅ Frontend index.html created
- ✅ Frontend package.json created
- ✅ Frontend CSS main.css created
- ⏳ Need to copy remaining CSS files
- ⏳ Need to copy all JS files
- ⏳ Need to create backend files
- ⏳ Need to create development scripts
