{"name": "nacinema-frontend", "version": "1.0.0", "description": "NaCinema Frontend - Movie Ticket Booking System (Vanilla JavaScript)", "main": "index.html", "scripts": {"dev": "npx http-server . -p 3000 -c-1 --cors", "start": "npx http-server . -p 3000 --cors", "build": "echo 'No build step required for vanilla version'", "test": "echo 'Open test-features.html in browser'", "preview": "npx http-server . -p 4173 --cors"}, "keywords": ["cinema", "movie", "booking", "vanilla-js", "frontend"], "author": "NaCinema Team", "license": "MIT", "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=16.0.0"}}