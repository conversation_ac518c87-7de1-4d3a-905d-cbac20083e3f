// ===== MODAL SYSTEM =====

class Modal {
    constructor() {
        this.overlay = null;
        this.content = null;
        this.currentModal = null;
        this.isOpen = false;
        this.init();
    }

    // Initialize modal system
    init() {
        this.overlay = utils.$('#modal-overlay');
        this.content = utils.$('#modal-content');
        
        if (!this.overlay || !this.content) {
            console.error('Modal elements not found');
            return;
        }

        this.setupEventListeners();
    }

    // Setup event listeners
    setupEventListeners() {
        // Close modal when clicking overlay
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.hide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.hide();
            }
        });

        // Handle close button clicks
        this.overlay.addEventListener('click', (e) => {
            if (e.target.closest('.modal-close')) {
                this.hide();
            }
        });
    }

    // Show modal with content
    show(content, options = {}) {
        const {
            title = '',
            size = 'medium',
            closable = true,
            backdrop = true,
            className = '',
            onShow = null,
            onHide = null
        } = options;

        // Create modal structure
        const modalHTML = `
            <div class="modal-dialog modal-${size} ${className}">
                <div class="modal-header">
                    ${title ? `<h3 class="modal-title">${title}</h3>` : ''}
                    ${closable ? '<button class="modal-close"><i class="fas fa-times"></i></button>' : ''}
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;

        this.content.innerHTML = modalHTML;
        this.overlay.style.display = 'flex';
        this.overlay.classList.add('show');
        this.isOpen = true;

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Store callbacks
        this.currentModal = { onShow, onHide };

        // Call onShow callback
        if (onShow) {
            setTimeout(onShow, 100);
        }

        // Focus management
        this.manageFocus();

        return this;
    }

    // Hide modal
    hide() {
        if (!this.isOpen) return;

        this.overlay.classList.remove('show');
        
        setTimeout(() => {
            this.overlay.style.display = 'none';
            this.content.innerHTML = '';
            this.isOpen = false;
            
            // Restore body scroll
            document.body.style.overflow = '';

            // Call onHide callback
            if (this.currentModal && this.currentModal.onHide) {
                this.currentModal.onHide();
            }

            this.currentModal = null;
        }, 300);

        return this;
    }

    // Manage focus for accessibility
    manageFocus() {
        const focusableElements = this.content.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    }

    // Check if modal is open
    isModalOpen() {
        return this.isOpen;
    }

    // Static methods for easy use
    static show(content, options = {}) {
        if (!window.modal) {
            window.modal = new Modal();
        }
        return window.modal.show(content, options);
    }

    static hide() {
        if (window.modal) {
            return window.modal.hide();
        }
    }

    static confirm(message, options = {}) {
        const {
            title = 'Xác nhận',
            confirmText = 'Xác nhận',
            cancelText = 'Hủy',
            onConfirm = null,
            onCancel = null
        } = options;

        return new Promise((resolve) => {
            const content = `
                <div class="confirm-modal">
                    <p class="confirm-message">${message}</p>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary" id="confirm-cancel">${cancelText}</button>
                        <button class="btn btn-primary" id="confirm-ok">${confirmText}</button>
                    </div>
                </div>
            `;

            Modal.show(content, {
                title,
                size: 'small',
                onShow: () => {
                    const cancelBtn = utils.$('#confirm-cancel');
                    const okBtn = utils.$('#confirm-ok');

                    cancelBtn.addEventListener('click', () => {
                        Modal.hide();
                        if (onCancel) onCancel();
                        resolve(false);
                    });

                    okBtn.addEventListener('click', () => {
                        Modal.hide();
                        if (onConfirm) onConfirm();
                        resolve(true);
                    });
                }
            });
        });
    }

    static alert(message, options = {}) {
        const {
            title = 'Thông báo',
            buttonText = 'OK'
        } = options;

        return new Promise((resolve) => {
            const content = `
                <div class="alert-modal">
                    <p class="alert-message">${message}</p>
                    <div class="alert-actions">
                        <button class="btn btn-primary" id="alert-ok">${buttonText}</button>
                    </div>
                </div>
            `;

            Modal.show(content, {
                title,
                size: 'small',
                onShow: () => {
                    const okBtn = utils.$('#alert-ok');
                    okBtn.addEventListener('click', () => {
                        Modal.hide();
                        resolve(true);
                    });
                }
            });
        });
    }

    static prompt(message, options = {}) {
        const {
            title = 'Nhập thông tin',
            placeholder = '',
            defaultValue = '',
            confirmText = 'OK',
            cancelText = 'Hủy'
        } = options;

        return new Promise((resolve) => {
            const content = `
                <div class="prompt-modal">
                    <p class="prompt-message">${message}</p>
                    <input type="text" class="form-input" id="prompt-input" 
                           placeholder="${placeholder}" value="${defaultValue}">
                    <div class="prompt-actions">
                        <button class="btn btn-secondary" id="prompt-cancel">${cancelText}</button>
                        <button class="btn btn-primary" id="prompt-ok">${confirmText}</button>
                    </div>
                </div>
            `;

            Modal.show(content, {
                title,
                size: 'small',
                onShow: () => {
                    const input = utils.$('#prompt-input');
                    const cancelBtn = utils.$('#prompt-cancel');
                    const okBtn = utils.$('#prompt-ok');

                    input.focus();
                    input.select();

                    const handleSubmit = () => {
                        const value = input.value.trim();
                        Modal.hide();
                        resolve(value || null);
                    };

                    const handleCancel = () => {
                        Modal.hide();
                        resolve(null);
                    };

                    input.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            handleSubmit();
                        }
                    });

                    cancelBtn.addEventListener('click', handleCancel);
                    okBtn.addEventListener('click', handleSubmit);
                }
            });
        });
    }

    static loading(message = 'Đang tải...') {
        const content = `
            <div class="loading-modal">
                <div class="spinner"></div>
                <p class="loading-message">${message}</p>
            </div>
        `;

        return Modal.show(content, {
            size: 'small',
            closable: false,
            backdrop: false
        });
    }
}

// Initialize modal system
document.addEventListener('DOMContentLoaded', () => {
    window.modal = new Modal();
});

// Export to global scope
window.Modal = Modal;
