// ===== NAVIGATION COMPONENT =====

class Navigation {
    constructor() {
        this.mobileMenuOpen = false;
        this.searchTimeout = null;
        this.init();
    }

    // Initialize navigation
    init() {
        this.setupMobileMenu();
        this.setupSearch();
        this.setupUserMenu();
        this.setupScrollEffect();
        
        // Update navigation based on auth state
        authUI.updateNavigation();
        authUI.setupGlobalClickHandler();
    }

    // Setup mobile menu functionality
    setupMobileMenu() {
        const mobileToggle = utils.$('#mobile-menu-toggle');
        const mobileMenu = utils.$('#mobile-menu');
        
        if (mobileToggle && mobileMenu) {
            mobileToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMobileMenu();
            });

            // Close mobile menu when clicking on links
            const mobileLinks = utils.$$('.mobile-nav-link');
            mobileLinks.forEach(link => {
                link.addEventListener('click', () => {
                    this.closeMobileMenu();
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileMenu.contains(e.target) && !mobileToggle.contains(e.target)) {
                    this.closeMobileMenu();
                }
            });
        }
    }

    // Toggle mobile menu
    toggleMobileMenu() {
        const mobileMenu = utils.$('#mobile-menu');
        const mobileToggle = utils.$('#mobile-menu-toggle');
        
        if (mobileMenu && mobileToggle) {
            this.mobileMenuOpen = !this.mobileMenuOpen;
            
            if (this.mobileMenuOpen) {
                mobileMenu.classList.add('show');
                mobileToggle.innerHTML = '<i class="fas fa-times"></i>';
                document.body.style.overflow = 'hidden'; // Prevent scroll
            } else {
                mobileMenu.classList.remove('show');
                mobileToggle.innerHTML = '<i class="fas fa-bars"></i>';
                document.body.style.overflow = '';
            }
        }
    }

    // Close mobile menu
    closeMobileMenu() {
        if (this.mobileMenuOpen) {
            this.toggleMobileMenu();
        }
    }

    // Setup search functionality
    setupSearch() {
        const searchInput = utils.$('#search-input');
        const searchBtn = utils.$('.search-btn');
        
        if (searchInput) {
            // Handle search input
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                
                // Debounce search
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.performSearch(query);
                }, 300);
            });

            // Handle enter key
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = e.target.value.trim();
                    this.performSearch(query);
                }
            });

            // Handle search button click
            if (searchBtn) {
                searchBtn.addEventListener('click', () => {
                    const query = searchInput.value.trim();
                    this.performSearch(query);
                });
            }

            // Handle focus/blur for mobile
            searchInput.addEventListener('focus', () => {
                if (utils.device.isMobile()) {
                    searchInput.parentElement.classList.add('focused');
                }
            });

            searchInput.addEventListener('blur', () => {
                if (utils.device.isMobile()) {
                    searchInput.parentElement.classList.remove('focused');
                }
            });
        }
    }

    // Perform search
    async performSearch(query) {
        if (!query) {
            // Clear search results if query is empty
            this.clearSearchResults();
            return;
        }

        try {
            // Show search loading
            this.showSearchLoading();

            // Search movies
            const movies = await movieAPI.getMovies({ search: query });
            
            // Update URL with search query
            utils.setQueryParam('search', query);
            
            // If we're not on home page, navigate to home with search
            if (window.router.getCurrentRoute() !== '/') {
                window.router.navigate(`/?search=${encodeURIComponent(query)}`);
            } else {
                // Update current page with search results
                if (window.pages && window.pages.home && window.pages.home.updateMovies) {
                    window.pages.home.updateMovies(movies);
                }
            }

            this.hideSearchLoading();

        } catch (error) {
            console.error('Search failed:', error);
            this.hideSearchLoading();
            window.toast.error('Lỗi tìm kiếm. Vui lòng thử lại.');
        }
    }

    // Show search loading
    showSearchLoading() {
        const searchBtn = utils.$('.search-btn');
        if (searchBtn) {
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        }
    }

    // Hide search loading
    hideSearchLoading() {
        const searchBtn = utils.$('.search-btn');
        if (searchBtn) {
            searchBtn.innerHTML = '<i class="fas fa-search"></i>';
        }
    }

    // Clear search results
    clearSearchResults() {
        utils.removeQueryParam('search');
        
        // Reset search input
        const searchInput = utils.$('#search-input');
        if (searchInput) {
            searchInput.value = '';
        }

        // Reload current page without search
        if (window.pages && window.pages.home && window.pages.home.loadMovies) {
            window.pages.home.loadMovies();
        }
    }

    // Setup user menu functionality
    setupUserMenu() {
        // This is handled in authUI.updateNavigation()
        // But we can add additional functionality here if needed
        
        // Handle user avatar click outside of auth system
        document.addEventListener('click', (e) => {
            const userAvatar = e.target.closest('#user-avatar');
            if (userAvatar) {
                e.stopPropagation();
                // Additional user menu logic can go here
            }
        });
    }

    // Setup scroll effect for navbar
    setupScrollEffect() {
        let lastScrollY = window.scrollY;
        let ticking = false;

        const updateNavbar = () => {
            const navbar = utils.$('.navbar');
            if (!navbar) return;

            const currentScrollY = window.scrollY;

            // Add scrolled class when scrolled down
            if (currentScrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Hide/show navbar on scroll (optional)
            if (currentScrollY > lastScrollY && currentScrollY > 100) {
                // Scrolling down
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // Scrolling up
                navbar.style.transform = 'translateY(0)';
            }

            lastScrollY = currentScrollY;
            ticking = false;
        };

        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavbar);
                ticking = true;
            }
        };

        window.addEventListener('scroll', onScroll, { passive: true });
    }

    // Update active navigation link
    updateActiveLink(currentPath) {
        // Remove active class from all nav links
        const navLinks = utils.$$('.nav-link, .mobile-nav-link');
        navLinks.forEach(link => link.classList.remove('active'));

        // Add active class to current route link
        const activeLinks = utils.$$(`[data-route="${currentPath}"]`);
        activeLinks.forEach(link => {
            if (link.classList.contains('nav-link') || link.classList.contains('mobile-nav-link')) {
                link.classList.add('active');
            }
        });
    }

    // Handle window resize
    handleResize() {
        // Close mobile menu on desktop
        if (window.innerWidth > 768 && this.mobileMenuOpen) {
            this.closeMobileMenu();
        }
    }

    // Destroy navigation (cleanup)
    destroy() {
        // Remove event listeners
        clearTimeout(this.searchTimeout);
        
        // Reset mobile menu state
        this.closeMobileMenu();
        document.body.style.overflow = '';
    }
}

// ===== NAVIGATION UTILITIES =====

const navigationUtils = {
    // Highlight navigation item
    highlightNavItem(path) {
        const navigation = window.navigation;
        if (navigation) {
            navigation.updateActiveLink(path);
        }
    },

    // Set search query
    setSearchQuery(query) {
        const searchInput = utils.$('#search-input');
        if (searchInput) {
            searchInput.value = query;
        }
    },

    // Get search query
    getSearchQuery() {
        const searchInput = utils.$('#search-input');
        return searchInput ? searchInput.value.trim() : '';
    },

    // Show/hide navigation
    showNavigation() {
        const navbar = utils.$('.navbar');
        if (navbar) {
            navbar.style.display = 'block';
        }
    },

    hideNavigation() {
        const navbar = utils.$('.navbar');
        if (navbar) {
            navbar.style.display = 'none';
        }
    }
};

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.navigation = new Navigation();
    
    // Handle window resize
    window.addEventListener('resize', utils.throttle(() => {
        window.navigation.handleResize();
    }, 250));
});

// Export to global scope
window.Navigation = Navigation;
window.navigationUtils = navigationUtils;
