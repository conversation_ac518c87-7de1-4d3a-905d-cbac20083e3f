// ===== MOVIE DETAIL PAGE =====

window.pages.movieDetail = {
    currentMovie: null,
    showtimes: [],
    selectedShowtime: null,
    seatMap: null,

    async render(movieId) {
        const mainContent = utils.$('#main-content');
        
        try {
            // Show loading
            mainContent.innerHTML = `
                <div class="container">
                    <div class="movie-detail-loading">
                        <div class="spinner"></div>
                        <p>Đang tải thông tin phim...</p>
                    </div>
                </div>
            `;

            // Load movie data
            await this.loadMovieData(movieId);
            
            // Render movie detail
            this.renderMovieDetail();
            
            // Setup event handlers
            this.setupEventHandlers();

        } catch (error) {
            console.error('Failed to load movie detail:', error);
            this.renderError('Không thể tải thông tin phim. Vui lòng thử lại.');
        }
    },

    async loadMovieData(movieId) {
        try {
            // Load movie and showtimes in parallel
            const [movie, showtimes] = await Promise.all([
                movieAPI.getMovie(movieId),
                movieAPI.getMovieShowtimes(movieId)
            ]);

            this.currentMovie = movie;
            this.showtimes = showtimes;
        } catch (error) {
            console.error('Failed to load movie data:', error);
            throw error;
        }
    },

    renderMovieDetail() {
        const mainContent = utils.$('#main-content');
        const movie = this.currentMovie;

        mainContent.innerHTML = `
            <div class="movie-detail-page">
                <!-- Movie Hero Section -->
                <div class="movie-hero">
                    <div class="movie-hero-background">
                        <img src="${movie.backdrop || movie.poster}" alt="${movie.title}">
                        <div class="movie-hero-overlay"></div>
                    </div>
                    
                    <div class="container">
                        <div class="movie-hero-content">
                            <div class="movie-poster-large">
                                <img src="${movie.poster}" alt="${movie.title}">
                                ${movie.status !== 'active' ? `
                                    <div class="movie-status-overlay">
                                        <span class="badge badge-secondary">
                                            ${movie.status === 'coming-soon' ? 'Sắp chiếu' : 'Ngừng chiếu'}
                                        </span>
                                    </div>
                                ` : ''}
                            </div>
                            
                            <div class="movie-info-main">
                                <h1 class="movie-title-large">${movie.title}</h1>
                                
                                <div class="movie-meta-large">
                                    <div class="meta-item">
                                        <i class="fas fa-star"></i>
                                        <span>8.5/10</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>${movie.duration} phút</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <span>${utils.formatDate(movie.releaseDate)}</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-tag"></i>
                                        <span>${movie.genre}</span>
                                    </div>
                                </div>
                                
                                <p class="movie-description-large">${movie.description}</p>
                                
                                <div class="movie-actions-large">
                                    ${movie.status === 'active' ? `
                                        <button class="btn btn-primary btn-lg" id="book-tickets-btn">
                                            <i class="fas fa-ticket-alt"></i>
                                            Đặt vé ngay
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-secondary btn-lg" id="add-to-wishlist">
                                        <i class="fas fa-heart"></i>
                                        Yêu thích
                                    </button>
                                    <button class="btn btn-secondary btn-lg" id="share-movie">
                                        <i class="fas fa-share"></i>
                                        Chia sẻ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container">
                    <!-- Showtimes Section -->
                    ${movie.status === 'active' ? this.renderShowtimesSection() : ''}
                    
                    <!-- Booking Section -->
                    <div id="booking-section" class="booking-section" style="display: none;">
                        <div class="section-header">
                            <h2 class="section-title">Chọn ghế ngồi</h2>
                            <div class="showtime-info" id="showtime-info">
                                <!-- Showtime info will be displayed here -->
                            </div>
                        </div>
                        
                        <div id="seatmap-container" class="seatmap-wrapper">
                            <!-- SeatMap will be rendered here -->
                        </div>
                        
                        <div class="booking-actions">
                            <button class="btn btn-secondary" id="back-to-showtimes">
                                <i class="fas fa-arrow-left"></i>
                                Quay lại chọn suất
                            </button>
                            <button class="btn btn-primary btn-lg" id="proceed-to-payment" disabled>
                                <i class="fas fa-credit-card"></i>
                                Tiếp tục thanh toán
                            </button>
                        </div>
                    </div>

                    <!-- Movie Details Tabs -->
                    <div class="movie-tabs">
                        <div class="tab-nav">
                            <button class="tab-btn active" data-tab="overview">Tổng quan</button>
                            <button class="tab-btn" data-tab="cast">Diễn viên</button>
                            <button class="tab-btn" data-tab="reviews">Đánh giá</button>
                        </div>
                        
                        <div class="tab-content">
                            <div class="tab-pane active" id="overview-tab">
                                <div class="movie-overview">
                                    <h3>Nội dung phim</h3>
                                    <p>${movie.description}</p>
                                    
                                    <div class="movie-details-grid">
                                        <div class="detail-item">
                                            <strong>Đạo diễn:</strong>
                                            <span>${movie.director || 'Đang cập nhật'}</span>
                                        </div>
                                        <div class="detail-item">
                                            <strong>Diễn viên:</strong>
                                            <span>${movie.cast || 'Đang cập nhật'}</span>
                                        </div>
                                        <div class="detail-item">
                                            <strong>Thể loại:</strong>
                                            <span>${movie.genre}</span>
                                        </div>
                                        <div class="detail-item">
                                            <strong>Thời lượng:</strong>
                                            <span>${movie.duration} phút</span>
                                        </div>
                                        <div class="detail-item">
                                            <strong>Ngày khởi chiếu:</strong>
                                            <span>${utils.formatDate(movie.releaseDate)}</span>
                                        </div>
                                        <div class="detail-item">
                                            <strong>Ngôn ngữ:</strong>
                                            <span>${movie.language || 'Tiếng Việt'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tab-pane" id="cast-tab">
                                <div class="cast-section">
                                    <h3>Diễn viên chính</h3>
                                    <p>Thông tin diễn viên đang được cập nhật...</p>
                                </div>
                            </div>
                            
                            <div class="tab-pane" id="reviews-tab">
                                <div class="reviews-section">
                                    <h3>Đánh giá từ khán giả</h3>
                                    <p>Chức năng đánh giá đang được phát triển...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderShowtimesSection() {
        if (!this.showtimes || this.showtimes.length === 0) {
            return `
                <div class="showtimes-section">
                    <div class="section-header">
                        <h2 class="section-title">Lịch chiếu</h2>
                    </div>
                    <div class="no-showtimes">
                        <i class="fas fa-calendar-times"></i>
                        <p>Hiện tại chưa có lịch chiếu cho phim này.</p>
                    </div>
                </div>
            `;
        }

        // Group showtimes by date
        const showtimesByDate = utils.groupBy(this.showtimes, 'date');
        
        return `
            <div class="showtimes-section" id="showtimes-section">
                <div class="section-header">
                    <h2 class="section-title">Lịch chiếu</h2>
                </div>
                
                <div class="showtimes-container">
                    ${Object.entries(showtimesByDate).map(([date, showtimes]) => `
                        <div class="showtime-date-group">
                            <h3 class="showtime-date">${utils.formatDate(date)}</h3>
                            <div class="showtime-grid">
                                ${showtimes.map(showtime => `
                                    <button class="showtime-btn" 
                                            data-showtime-id="${showtime.id}"
                                            data-showtime='${JSON.stringify(showtime)}'>
                                        <div class="showtime-time">${utils.formatTime(showtime.startTime)}</div>
                                        <div class="showtime-info">
                                            <span class="cinema-name">${showtime.cinema.name}</span>
                                            <span class="room-name">Phòng ${showtime.room.name}</span>
                                        </div>
                                        <div class="showtime-price">${utils.formatPrice(showtime.price)}</div>
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    setupEventHandlers() {
        // Book tickets button
        const bookTicketsBtn = utils.$('#book-tickets-btn');
        if (bookTicketsBtn) {
            bookTicketsBtn.addEventListener('click', () => {
                this.scrollToShowtimes();
            });
        }

        // Showtime selection
        const showtimeBtns = utils.$$('.showtime-btn');
        showtimeBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const showtimeData = JSON.parse(e.currentTarget.dataset.showtime);
                this.selectShowtime(showtimeData);
            });
        });

        // Tab navigation
        const tabBtns = utils.$$('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // Back to showtimes
        const backBtn = utils.$('#back-to-showtimes');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.backToShowtimes();
            });
        }

        // Proceed to payment
        const paymentBtn = utils.$('#proceed-to-payment');
        if (paymentBtn) {
            paymentBtn.addEventListener('click', () => {
                this.proceedToPayment();
            });
        }

        // Wishlist and share buttons
        this.setupActionButtons();
    },

    setupActionButtons() {
        const wishlistBtn = utils.$('#add-to-wishlist');
        const shareBtn = utils.$('#share-movie');

        if (wishlistBtn) {
            wishlistBtn.addEventListener('click', () => {
                Toast.success('Đã thêm vào danh sách yêu thích!');
            });
        }

        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                if (navigator.share) {
                    navigator.share({
                        title: this.currentMovie.title,
                        text: this.currentMovie.description,
                        url: window.location.href
                    });
                } else {
                    // Fallback: copy to clipboard
                    navigator.clipboard.writeText(window.location.href);
                    Toast.success('Đã sao chép link phim!');
                }
            });
        }
    },

    scrollToShowtimes() {
        const showtimesSection = utils.$('#showtimes-section');
        if (showtimesSection) {
            showtimesSection.scrollIntoView({ behavior: 'smooth' });
        }
    },

    async selectShowtime(showtime) {
        this.selectedShowtime = showtime;
        
        // Update UI
        this.updateShowtimeSelection();
        this.showBookingSection();
        
        // Initialize seat map
        await this.initializeSeatMap();
    },

    updateShowtimeSelection() {
        // Remove active class from all showtime buttons
        utils.$$('.showtime-btn').forEach(btn => btn.classList.remove('active'));
        
        // Add active class to selected showtime
        const selectedBtn = utils.$(`[data-showtime-id="${this.selectedShowtime.id}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('active');
        }

        // Update showtime info
        const showtimeInfo = utils.$('#showtime-info');
        if (showtimeInfo) {
            showtimeInfo.innerHTML = `
                <div class="selected-showtime">
                    <div class="showtime-details">
                        <strong>${utils.formatTime(this.selectedShowtime.startTime)}</strong>
                        <span>${this.selectedShowtime.cinema.name} - Phòng ${this.selectedShowtime.room.name}</span>
                    </div>
                    <div class="showtime-price">${utils.formatPrice(this.selectedShowtime.price)}</div>
                </div>
            `;
        }
    },

    showBookingSection() {
        const bookingSection = utils.$('#booking-section');
        const showtimesSection = utils.$('#showtimes-section');
        
        if (bookingSection) {
            bookingSection.style.display = 'block';
            bookingSection.scrollIntoView({ behavior: 'smooth' });
        }
        
        if (showtimesSection) {
            showtimesSection.style.display = 'none';
        }
    },

    backToShowtimes() {
        const bookingSection = utils.$('#booking-section');
        const showtimesSection = utils.$('#showtimes-section');
        
        if (bookingSection) {
            bookingSection.style.display = 'none';
        }
        
        if (showtimesSection) {
            showtimesSection.style.display = 'block';
            showtimesSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Destroy seat map
        if (this.seatMap) {
            this.seatMap.destroy();
            this.seatMap = null;
        }

        this.selectedShowtime = null;
    },

    async initializeSeatMap() {
        const container = utils.$('#seatmap-container');
        if (!container || !this.selectedShowtime) return;

        try {
            // Destroy existing seat map
            if (this.seatMap) {
                this.seatMap.destroy();
            }

            // Create new seat map
            this.seatMap = new SeatMap(container, this.selectedShowtime.id, {
                seatPrice: this.selectedShowtime.price,
                onSelectionChange: (selectedSeats) => {
                    this.updatePaymentButton(selectedSeats);
                }
            });

        } catch (error) {
            console.error('Failed to initialize seat map:', error);
            Toast.error('Không thể tải sơ đồ ghế. Vui lòng thử lại.');
        }
    },

    updatePaymentButton(selectedSeats) {
        const paymentBtn = utils.$('#proceed-to-payment');
        if (paymentBtn) {
            paymentBtn.disabled = selectedSeats.length === 0;
            
            if (selectedSeats.length > 0) {
                paymentBtn.innerHTML = `
                    <i class="fas fa-credit-card"></i>
                    Thanh toán (${selectedSeats.length} ghế)
                `;
            } else {
                paymentBtn.innerHTML = `
                    <i class="fas fa-credit-card"></i>
                    Tiếp tục thanh toán
                `;
            }
        }
    },

    proceedToPayment() {
        if (!this.seatMap || !this.selectedShowtime) return;

        const selectedSeats = this.seatMap.getSelectedSeats();
        if (selectedSeats.length === 0) {
            Toast.warning('Vui lòng chọn ghế trước khi thanh toán.');
            return;
        }

        // Store booking data
        const bookingData = {
            movie: this.currentMovie,
            showtime: this.selectedShowtime,
            seats: selectedSeats,
            totalPrice: this.seatMap.calculateTotalPrice()
        };

        utils.storage.set('pendingBooking', bookingData);
        
        // Navigate to payment page
        window.router.navigate(`/booking/${this.selectedShowtime.id}`);
    },

    switchTab(tabId) {
        // Remove active class from all tabs
        utils.$$('.tab-btn').forEach(btn => btn.classList.remove('active'));
        utils.$$('.tab-pane').forEach(pane => pane.classList.remove('active'));

        // Add active class to selected tab
        const tabBtn = utils.$(`[data-tab="${tabId}"]`);
        const tabPane = utils.$(`#${tabId}-tab`);

        if (tabBtn) tabBtn.classList.add('active');
        if (tabPane) tabPane.classList.add('active');
    },

    renderError(message) {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <div class="error-page">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle error-icon"></i>
                        <h2>Lỗi tải phim</h2>
                        <p>${message}</p>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="window.location.reload()">
                                Thử lại
                            </button>
                            <a href="#" class="btn btn-secondary" data-route="/">
                                Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};
