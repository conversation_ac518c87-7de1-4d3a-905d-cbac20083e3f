# 🎬 NaCinema - Vanilla Version

## 📋 Tổng quan

Đây là phiên bản HTML/CSS/JavaScript thuần của dự án <PERSON>, đượ<PERSON> chuyển đổi từ React để loại bỏ các dependencies phức tạp và tăng hiệu suất.

## ✨ Tính năng đã hoàn thành

### 🏗️ **Core Architecture**
- ✅ **Client-side Routing** - Điều hướng SPA không reload trang
- ✅ **State Management** - Quản lý trạng thái global
- ✅ **API Client** - HTTP client với caching tự động
- ✅ **Authentication** - JWT authentication với localStorage
- ✅ **Query Cache** - Caching API responses (thay thế React Query)

### 🎨 **UI Components**
- ✅ **Navigation** - Responsive navbar với mobile menu
- ✅ **Modal System** - Popup dialogs với confirm/alert/prompt
- ✅ **Toast Notifications** - Success/error/warning messages
- ✅ **SeatMap** - Interactive seat selection component
- ✅ **Movie Cards** - Movie display với hover effects
- ✅ **Forms** - Login/register forms với validation

### 📱 **Pages**
- ✅ **Home Page** - Movie grid với filters và search
- ✅ **Movie Detail** - Chi tiết phim với showtimes và booking
- ✅ **Login/Register** - Authentication pages
- ✅ **Seat Selection** - Interactive seat map
- 🚧 **Booking Flow** - Payment process (cơ bản)
- 🚧 **User Dashboard** - Booking history (placeholder)
- 🚧 **Admin Panel** - Management interface (placeholder)

### 🎯 **Features**
- ✅ **Responsive Design** - Mobile-first approach
- ✅ **Dark Theme** - Modern dark UI
- ✅ **Search** - Real-time movie search
- ✅ **Filters** - Filter movies by status/genre
- ✅ **Authentication** - Login/logout/register
- ✅ **Route Protection** - Protected routes cho authenticated users
- ✅ **Error Handling** - Comprehensive error handling
- ✅ **Loading States** - Loading spinners và skeletons

## 🚀 Cách sử dụng

### 1. **Cài đặt**

```bash
# Clone repository
git clone <repository-url>
cd MovieNA

# Backend vẫn giữ nguyên - chạy server Node.js
npm install
npm start

# Frontend - serve static files từ vanilla-version/
cd vanilla-version
```

### 2. **Serve Frontend**

Bạn có thể sử dụng bất kỳ web server nào:

```bash
# Python
python -m http.server 8080

# Node.js
npx serve .

# PHP
php -S localhost:8080

# Live Server (VS Code extension)
# Right-click index.html -> Open with Live Server
```

### 3. **Truy cập**

- Frontend: `http://localhost:8080`
- Backend API: `http://localhost:3000` (hoặc port của backend)

## 📁 Cấu trúc thư mục

```
vanilla-version/
├── index.html              # Entry point
├── css/
│   ├── main.css            # Base styles + CSS variables
│   ├── components.css      # Component styles
│   └── responsive.css      # Responsive design
├── js/
│   ├── utils.js           # Utility functions
│   ├── api.js             # API client + caching
│   ├── auth.js            # Authentication system
│   ├── router.js          # Client-side routing
│   ├── app.js             # Main application
│   ├── components/
│   │   ├── navigation.js   # Navigation component
│   │   ├── modal.js        # Modal system
│   │   ├── toast.js        # Toast notifications
│   │   └── seatmap.js      # Seat selection
│   └── pages/
│       ├── home.js         # Home page (in app.js)
│       ├── movie-detail.js # Movie detail page
│       ├── login.js        # Login page
│       └── register.js     # Register page
├── assets/                 # Images, icons
└── README.md              # This file
```

## 🔧 API Integration

### Backend Requirements

Backend cần cung cấp các endpoints sau:

```javascript
// Authentication
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout
GET  /api/auth/me
POST /api/auth/refresh

// Movies
GET  /api/movies
GET  /api/movies/:id
GET  /api/movies/:id/showtimes

// Showtimes
GET  /api/showtimes
GET  /api/showtimes/:id
GET  /api/showtimes/:id/seats

// Bookings
POST /api/bookings
GET  /api/tickets
GET  /api/tickets/:code

// Admin
GET  /api/admin/dashboard
GET  /api/admin/users
```

### API Client Usage

```javascript
// Get movies
const movies = await movieAPI.getMovies({ status: 'active' });

// Create booking
const booking = await bookingAPI.createBooking({
    showtimeId: '123',
    seats: ['A1', 'A2'],
    totalPrice: 200000
});

// With caching
const cachedMovies = await queryCache.query('movies', 
    () => movieAPI.getMovies()
);
```

## 🎨 Styling System

### CSS Variables

```css
:root {
    /* Colors */
    --primary-red: #dc2626;
    --gray-900: #111827;
    --white: #ffffff;
    
    /* Spacing */
    --spacing-4: 1rem;
    --spacing-8: 2rem;
    
    /* Typography */
    --font-size-base: 1rem;
    --font-weight-medium: 500;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
}
```

### Utility Classes

```css
.container { max-width: 1280px; margin: 0 auto; }
.btn { padding: 0.75rem 1.5rem; border-radius: 0.375rem; }
.card { background: var(--gray-800); border-radius: 0.5rem; }
.fade-in { animation: fadeIn 300ms ease-in-out; }
```

## 🔐 Authentication

### Usage

```javascript
// Login
await auth.login({ username: 'user', password: 'pass' });

// Check authentication
if (auth.isAuthenticated()) {
    // User is logged in
}

// Check permissions
if (auth.hasRole('admin')) {
    // Show admin features
}

// Logout
await auth.logout();
```

### Route Protection

```javascript
// Protected routes
router.route('/admin', async () => {
    if (!auth.hasRole('admin')) {
        router.navigate('/login');
        return;
    }
    await pages.admin.render();
});
```

## 📱 Components

### Modal

```javascript
// Basic modal
Modal.show('<p>Hello World!</p>', {
    title: 'Greeting',
    size: 'medium'
});

// Confirm dialog
const confirmed = await Modal.confirm('Are you sure?');

// Alert
await Modal.alert('Success!');

// Prompt
const name = await Modal.prompt('Enter your name:');
```

### Toast

```javascript
// Success message
Toast.success('Operation completed!');

// Error message
Toast.error('Something went wrong!');

// Warning
Toast.warning('Please check your input');

// Info
Toast.info('New update available');
```

### SeatMap

```javascript
const seatMap = new SeatMap('#seatmap-container', showtimeId, {
    maxSeats: 8,
    seatPrice: 100000,
    onSelectionChange: (selectedSeats) => {
        console.log('Selected:', selectedSeats);
    }
});
```

## 🚧 Phát triển tiếp

### Ưu tiên cao
1. **Payment Integration** - Tích hợp thanh toán
2. **Booking Management** - Quản lý đặt vé
3. **User Dashboard** - Dashboard người dùng
4. **Admin Panel** - Panel quản trị

### Ưu tiên trung bình
1. **PWA Features** - Service worker, offline support
2. **Performance** - Code splitting, lazy loading
3. **Testing** - Unit tests, integration tests
4. **SEO** - Meta tags, structured data

### Cách thêm tính năng mới

1. **Tạo component mới:**
```javascript
// js/components/new-component.js
class NewComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.options = options;
        this.init();
    }
    
    init() {
        this.render();
        this.setupEventHandlers();
    }
    
    render() {
        // Render component
    }
    
    setupEventHandlers() {
        // Setup event listeners
    }
}

window.NewComponent = NewComponent;
```

2. **Tạo page mới:**
```javascript
// js/pages/new-page.js
window.pages.newPage = {
    async render() {
        const mainContent = utils.$('#main-content');
        mainContent.innerHTML = `
            <div class="container">
                <h1>New Page</h1>
            </div>
        `;
    }
};
```

3. **Thêm route:**
```javascript
// js/router.js
router.route('/new-page', async () => {
    await window.pages.newPage.render();
});
```

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Kiểm tra backend server đang chạy
   - Kiểm tra CORS settings
   - Kiểm tra API endpoints

2. **Authentication Not Working**
   - Kiểm tra localStorage
   - Kiểm tra JWT token format
   - Kiểm tra API auth endpoints

3. **Routing Issues**
   - Kiểm tra browser history API support
   - Kiểm tra route definitions
   - Kiểm tra base URL

4. **Styling Issues**
   - Kiểm tra CSS file loading order
   - Kiểm tra CSS variables support
   - Kiểm tra responsive breakpoints

## 📞 Support

Nếu gặp vấn đề, hãy:
1. Kiểm tra browser console cho errors
2. Kiểm tra network tab cho API calls
3. Kiểm tra localStorage cho auth data
4. Tham khảo `CONVERSION_GUIDE.md` cho chi tiết

## 🎉 Kết luận

Phiên bản vanilla này cung cấp:
- ⚡ **Performance tốt hơn** - Không có React overhead
- 🔧 **Dễ maintain** - Code đơn giản, dễ hiểu
- 📦 **Bundle size nhỏ** - Chỉ code cần thiết
- 🚀 **Fast loading** - Ít dependencies
- 💪 **Full control** - Kiểm soát hoàn toàn code

Bạn có thể tiếp tục phát triển dựa trên foundation này!
