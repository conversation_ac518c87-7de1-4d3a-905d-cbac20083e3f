# NaCinema - Movie Ticket Booking System

## Overview

NaCinema is a full-stack web application for movie ticket booking built with modern technologies. The system provides a complete cinema experience with movie listings, seat selection, ticket booking, and management features for different user roles.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **UI Library**: Radix UI components with shadcn/ui design system
- **Styling**: Tailwind CSS with custom cinema-themed color variables
- **State Management**: React Query (TanStack Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod validation
- **Build Tool**: Vite with hot reload and development optimizations

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database**: In-memory storage (MemStorage) for fast prototyping
- **Authentication**: JWT tokens with bcrypt password hashing
- **API Design**: RESTful endpoints with role-based access control

### Project Structure
```
/client          # React frontend
  /src
    /components  # Reusable UI components
    /pages       # Route components
    /lib         # Utilities and configurations
    /hooks       # Custom React hooks
/server          # Express backend
/shared          # Shared types and schemas
/migrations      # Database migrations
```

## Key Components

### Authentication System
- JWT-based authentication with localStorage storage
- Role-based access control (user, staff, admin)
- Secure password hashing with bcrypt
- Protected routes and middleware

### User Management
- Three user roles with distinct permissions:
  - **User**: Browse movies, book tickets, view bookings
  - **Staff**: Manage showtimes, approve/cancel bookings
  - **Admin**: Full system control, manage movies, cinemas, analytics

### Movie Management
- Complete movie catalog with metadata (title, genre, duration, rating)
- Poster and trailer support
- Movie status management (active/inactive)
- Review and rating system

### Cinema & Showtime Management
- Multi-cinema support with room configurations
- Flexible seat layout system using JSON configuration
- Dynamic pricing per showtime
- Real-time seat availability tracking

### Booking System
- Interactive seat selection with visual seat map
- 4 seat types with different pricing: Regular, VIP (+50%), Sweet (+30%), Premium (+20%)
- Booking validation and conflict prevention
- Multiple payment method support (cash, card, MoMo, banking)
- Promotion code integration with validation
- Booking confirmation with unique codes

### Promotion System
- Discount codes with percentage or fixed amount
- Usage limits and minimum purchase requirements
- Expiration date management
- Real-time validation during booking process

## Data Flow

### User Journey
1. **Browse Movies**: Users view available movies with details and showtimes
2. **Select Showtime**: Choose cinema, room, and time slot
3. **Seat Selection**: Interactive seat map with real-time availability
4. **Booking**: Form submission with customer details and payment
5. **Confirmation**: Booking code generation and ticket details

### Admin/Staff Workflow
1. **Dashboard Statistics**: Real-time overview of system metrics
2. **Movie Management**: CRUD operations on movie catalog with status control
3. **Showtime Scheduling**: Create and manage screening schedules
4. **Booking Oversight**: Monitor, approve, or cancel bookings
5. **Analytics**: View booking statistics and revenue reports
6. **Promotion Management**: Create and manage discount campaigns

### Database Schema
- **Users**: Authentication and profile information
- **Movies**: Movie catalog with metadata and status (active/coming-soon)
- **Cinemas/Rooms**: Theater infrastructure
- **Showtimes**: Scheduling with pricing and seat management
- **Tickets**: Booking records with seat assignments and customer info
- **Reviews**: User feedback and ratings system
- **Promotions**: Discount codes and marketing campaigns

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: Serverless PostgreSQL connection
- **drizzle-orm**: Type-safe database ORM
- **@tanstack/react-query**: Server state management
- **@radix-ui/***: Accessible UI components
- **wouter**: Lightweight React router
- **zod**: Schema validation
- **jwt**: Authentication tokens
- **bcrypt**: Password hashing

### Development Tools
- **Vite**: Fast build tool and dev server
- **TypeScript**: Type safety and better DX
- **Tailwind CSS**: Utility-first styling
- **ESLint/Prettier**: Code quality and formatting

## Deployment Strategy

### Development Environment
- Vite development server with hot reload
- In-memory storage for rapid prototyping
- Replit-specific optimizations and debugging tools

### Production Build
- Vite builds optimized frontend bundle
- esbuild compiles backend for Node.js
- Single artifact deployment with static file serving

### Database Management
- In-memory storage with full CRUD operations
- Mock data for development and testing
- Easy migration to persistent storage when needed

### Environment Configuration
- Database URL from environment variables
- JWT secret configuration
- Development vs production feature flags

## Recent Changes (December 2024)

### Phase 1: Core System (Completed)
- ✅ Complete user authentication with JWT tokens
- ✅ Movie catalog with detailed information
- ✅ Cinema and showtime management
- ✅ Basic booking system

### Phase 2: Enhanced Features (Completed)
- ✅ **Upcoming Movies Section**: Added "Phim sắp chiếu" with coming-soon status
- ✅ **Promotion System**: Discount codes with validation and expiration
- ✅ **Advanced Seat Selection**: 4 seat types with dynamic pricing
- ✅ **Complete Booking Flow**: Form validation, payment methods, customer info
- ✅ **Review System**: Movie ratings and user feedback
- ✅ **Admin Dashboard**: Statistics overview with real-time metrics

### Phase 3: Critical Bug Fixes (Completed July 2025)
- ✅ **Seat Conflict Prevention**: Enhanced booking validation to prevent double-booking
- ✅ **Real-time Seat Updates**: Fixed seat availability display in admin panel
- ✅ **Robust Error Handling**: Improved error messages for booking conflicts
- ✅ **Data Consistency**: Added double-check validation against existing tickets
- ✅ **Query Invalidation**: Proper cache refresh for real-time updates

### Phase 4: Development Setup & Documentation (Completed July 2025)
- ✅ **Complete VS Code Setup Guide**: Created comprehensive README.md with installation instructions
- ✅ **VS Code Configuration**: Added .vscode/ folder with extensions, settings, tasks, and launch configs
- ✅ **MongoDB Atlas Integration**: Fully configured with connection strings and environment setup
- ✅ **Development Tooling**: Added Prettier, ESLint, TypeScript debugging support
- ✅ **Troubleshooting Guide**: Created SETUP_GUIDE.md with common issues and solutions
- ✅ **Docker Containerization**: Full Docker setup with multi-stage builds, health checks, and orchestration
- ✅ **Docker Compose**: Separate configs for development, production, and testing environments
- ✅ **Nginx Reverse Proxy**: Load balancing, rate limiting, and SSL termination
- ✅ **Redis Caching**: Added Redis container for session storage and caching layer
- ✅ **Docker Scripts**: Automated deployment and maintenance scripts
- ✅ **Docker Documentation**: Comprehensive DOCKER_GUIDE.md with best practices

### Phase 5: Next Steps
- 🔄 Mobile responsiveness improvements
- 🔄 Email notifications for bookings
- 🔄 Advanced analytics and reporting
- 🔄 Staff management interface

The application follows modern web development practices with a focus on type safety, performance, and maintainability. The architecture supports scalability and easy feature additions while maintaining a clean separation of concerns between frontend and backend.