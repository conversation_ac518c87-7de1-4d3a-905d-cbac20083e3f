// ===== LOGIN PAGE =====

window.pages.login = {
    async render() {
        const mainContent = utils.$('#main-content');
        
        mainContent.innerHTML = `
            <div class="auth-page">
                <div class="auth-container">
                    <div class="auth-card">
                        <div class="auth-header">
                            <div class="auth-logo">
                                <i class="fas fa-film"></i>
                                <span>NaCinema</span>
                            </div>
                            <h2>Đăng nhập</h2>
                            <p>Chào mừng bạn quay trở lại!</p>
                        </div>

                        <form id="login-form" class="auth-form">
                            <div class="form-group">
                                <label class="form-label" for="username">Tên đăng nhập hoặc Email</label>
                                <input type="text" 
                                       id="username" 
                                       name="username" 
                                       class="form-input" 
                                       placeholder="Nhập tên đăng nhập hoặc email"
                                       required>
                                <div class="form-error" id="username-error"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="password">Mật khẩu</label>
                                <div class="password-input-group">
                                    <input type="password" 
                                           id="password" 
                                           name="password" 
                                           class="form-input" 
                                           placeholder="Nhập mật khẩu"
                                           required>
                                    <button type="button" class="password-toggle" id="password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-error" id="password-error"></div>
                            </div>

                            <div class="form-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="remember-me" name="remember">
                                    <span class="checkmark"></span>
                                    Ghi nhớ đăng nhập
                                </label>
                                
                                <a href="#" class="forgot-password" id="forgot-password">
                                    Quên mật khẩu?
                                </a>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg auth-submit" id="login-btn">
                                <span class="btn-text">Đăng nhập</span>
                                <div class="btn-spinner" style="display: none;">
                                    <div class="spinner"></div>
                                </div>
                            </button>

                            <div class="auth-divider">
                                <span>Hoặc đăng nhập với</span>
                            </div>

                            <div class="social-login">
                                <button type="button" class="btn btn-social btn-google">
                                    <i class="fab fa-google"></i>
                                    Google
                                </button>
                                <button type="button" class="btn btn-social btn-facebook">
                                    <i class="fab fa-facebook-f"></i>
                                    Facebook
                                </button>
                            </div>
                        </form>

                        <div class="auth-footer">
                            <p>Chưa có tài khoản? 
                                <a href="#" data-route="/register" class="auth-link">Đăng ký ngay</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupEventHandlers();
    },

    setupEventHandlers() {
        // Form submission
        const loginForm = utils.$('#login-form');
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Password toggle
        const passwordToggle = utils.$('#password-toggle');
        const passwordInput = utils.$('#password');
        
        passwordToggle.addEventListener('click', () => {
            const type = passwordInput.type === 'password' ? 'text' : 'password';
            passwordInput.type = type;
            
            const icon = passwordToggle.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        });

        // Forgot password
        const forgotPassword = utils.$('#forgot-password');
        forgotPassword.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleForgotPassword();
        });

        // Social login
        const googleBtn = utils.$('.btn-google');
        const facebookBtn = utils.$('.btn-facebook');

        googleBtn.addEventListener('click', () => {
            this.handleSocialLogin('google');
        });

        facebookBtn.addEventListener('click', () => {
            this.handleSocialLogin('facebook');
        });

        // Auto-focus first input
        const usernameInput = utils.$('#username');
        if (usernameInput) {
            usernameInput.focus();
        }

        // Enter key handling
        const inputs = utils.$$('#login-form input');
        inputs.forEach((input, index) => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    if (index === inputs.length - 1) {
                        this.handleLogin();
                    } else {
                        inputs[index + 1].focus();
                    }
                }
            });
        });
    },

    async handleLogin() {
        const form = utils.$('#login-form');
        const formData = new FormData(form);
        const loginBtn = utils.$('#login-btn');
        
        const credentials = {
            username: formData.get('username').trim(),
            password: formData.get('password'),
            remember: formData.get('remember') === 'on'
        };

        // Validate form
        if (!this.validateForm(credentials)) {
            return;
        }

        try {
            // Show loading
            this.setLoading(true);
            this.clearErrors();

            // Attempt login
            const response = await auth.login(credentials);

            // Success
            Toast.success('Đăng nhập thành công!');
            
            // Redirect to intended page or home
            const redirectTo = utils.getQueryParams().redirect || '/';
            window.router.navigate(redirectTo);

        } catch (error) {
            console.error('Login failed:', error);
            
            // Handle different error types
            if (error.status === 401) {
                this.showError('username', 'Tên đăng nhập hoặc mật khẩu không đúng');
            } else if (error.status === 429) {
                this.showError('general', 'Quá nhiều lần thử. Vui lòng thử lại sau.');
            } else {
                this.showError('general', 'Đăng nhập thất bại. Vui lòng thử lại.');
            }
            
            Toast.error(error.message || 'Đăng nhập thất bại');
        } finally {
            this.setLoading(false);
        }
    },

    validateForm(credentials) {
        let isValid = true;
        
        // Clear previous errors
        this.clearErrors();

        // Validate username
        if (!credentials.username) {
            this.showError('username', 'Vui lòng nhập tên đăng nhập hoặc email');
            isValid = false;
        } else if (credentials.username.length < 3) {
            this.showError('username', 'Tên đăng nhập phải có ít nhất 3 ký tự');
            isValid = false;
        }

        // Validate password
        if (!credentials.password) {
            this.showError('password', 'Vui lòng nhập mật khẩu');
            isValid = false;
        } else if (credentials.password.length < 6) {
            this.showError('password', 'Mật khẩu phải có ít nhất 6 ký tự');
            isValid = false;
        }

        return isValid;
    },

    showError(field, message) {
        const errorElement = utils.$(`#${field}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        // Add error class to input
        const inputElement = utils.$(`#${field}`);
        if (inputElement) {
            inputElement.classList.add('error');
        }
    },

    clearErrors() {
        const errorElements = utils.$$('.form-error');
        errorElements.forEach(element => {
            element.textContent = '';
            element.style.display = 'none';
        });

        const inputElements = utils.$$('.form-input');
        inputElements.forEach(element => {
            element.classList.remove('error');
        });
    },

    setLoading(loading) {
        const loginBtn = utils.$('#login-btn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnSpinner = loginBtn.querySelector('.btn-spinner');

        if (loading) {
            loginBtn.disabled = true;
            btnText.style.display = 'none';
            btnSpinner.style.display = 'flex';
        } else {
            loginBtn.disabled = false;
            btnText.style.display = 'block';
            btnSpinner.style.display = 'none';
        }
    },

    async handleForgotPassword() {
        const email = await Modal.prompt(
            'Nhập email của bạn để nhận link đặt lại mật khẩu:',
            {
                title: 'Quên mật khẩu',
                placeholder: '<EMAIL>',
                confirmText: 'Gửi',
                cancelText: 'Hủy'
            }
        );

        if (email && utils.validators.email(email)) {
            try {
                // Call forgot password API
                await api.post('/auth/forgot-password', { email });
                Toast.success('Link đặt lại mật khẩu đã được gửi đến email của bạn!');
            } catch (error) {
                Toast.error('Không thể gửi email. Vui lòng thử lại.');
            }
        } else if (email) {
            Toast.error('Email không hợp lệ');
        }
    },

    async handleSocialLogin(provider) {
        try {
            Toast.info(`Đang chuyển hướng đến ${provider}...`);
            
            // In a real app, this would redirect to OAuth provider
            // For demo, we'll show a message
            setTimeout(() => {
                Toast.warning('Tính năng đăng nhập mạng xã hội đang được phát triển');
            }, 1000);
            
        } catch (error) {
            Toast.error(`Đăng nhập ${provider} thất bại`);
        }
    }
};
